#!/usr/bin/env python3
"""
测试 FastMCP 客户端包装器
"""
import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from backend.core.mcp.fastmcp_client import FastMCPClientWrapper
from backend.core.mcp.config import MCPTransportType, MCPServerConfig

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_fastmcp_wrapper():
    """测试 FastMCP 客户端包装器"""
    print("🚀 测试 FastMCP 客户端包装器")
    print("请确保城市分级查询 MCP 服务器正在运行 (python mcp_server/city/server.py)")
    print("服务器应该运行在 http://localhost:8080/sse")
    print()
    
    # 创建 FastMCP 客户端包装器
    client = FastMCPClientWrapper()
    
    # 添加服务器配置
    server_config = MCPServerConfig(
        name="city",
        transport_type=MCPTransportType.SSE,
        url="http://localhost:8080/sse",
        timeout=30,
        retry_count=3
    )
    
    client.add_server(server_config)
    
    try:
        # 启动服务器
        print("1. 启动 MCP 服务器...")
        success = await client.start_server("city")
        if not success:
            print("❌ 服务器启动失败")
            return
        
        print("✅ 服务器启动成功")
        
        # 获取服务器状态
        print("\n2. 获取服务器状态...")
        status = client.get_server_status("city")
        print("状态:", json.dumps(status, indent=2, ensure_ascii=False, default=str))
        
        # 获取可用工具
        print("\n3. 获取可用工具...")
        tools = client.get_available_tools()
        print(f"发现 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 测试工具调用
        print("\n4. 测试工具调用...")
        tool_name = "get_city_tier"
        parameters = {"city_name": "北京"}
        
        print(f"测试工具: {tool_name}")
        print(f"参数: {parameters}")
        
        # 调用工具
        try:
            print("\n开始调用工具...")
            result = await client.call_tool(tool_name, parameters)
            print("✅ 工具调用成功")
            print("结果:", json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试另一个工具
        print("\n5. 测试搜索工具...")
        try:
            search_result = await client.call_tool("search_cities", {"keyword": "北"})
            print("✅ 搜索工具调用成功")
            print("搜索结果:", json.dumps(search_result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ 搜索工具调用失败: {e}")
        
        # 测试获取所有分级
        print("\n6. 测试获取所有分级...")
        try:
            tiers_result = await client.call_tool("get_all_tiers", {})
            print("✅ 获取所有分级成功")
            print("分级结果:", json.dumps(tiers_result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ 获取所有分级失败: {e}")
        
    finally:
        # 停止服务器
        print("\n7. 停止服务器...")
        await client.stop_server("city")
        print("✅ 服务器已停止")
    
    print("\n🎉 测试完成")

async def test_direct_fastmcp():
    """直接测试 FastMCP 客户端"""
    print("\n🔧 直接测试 FastMCP 客户端")
    print("=" * 50)
    
    try:
        from fastmcp import Client
        
        # 创建客户端
        client = Client("http://localhost:8080/sse")
        
        print("1. 测试连接和工具列表...")
        async with client:
            # 获取工具列表
            tools = await client.list_tools()
            print(f"   ✅ 获取到 {len(tools) if hasattr(tools, '__len__') else 'unknown'} 个工具")
            print(f"   工具类型: {type(tools)}")
            
            if hasattr(tools, '__iter__'):
                for i, tool in enumerate(tools):
                    print(f"   工具 {i}: {type(tool)} - {tool}")
                    if hasattr(tool, 'name'):
                        print(f"     名称: {tool.name}")
                    if hasattr(tool, 'description'):
                        print(f"     描述: {tool.description}")
                    if i >= 2:  # 只显示前3个工具
                        break
            
            # 测试工具调用
            print("\n2. 测试工具调用...")
            try:
                result = await client.call_tool("get_city_tier", {"city_name": "北京"})
                print(f"   ✅ 工具调用成功: {type(result)}")
                print(f"   结果: {result}")
                
                if hasattr(result, '__iter__') and not isinstance(result, str):
                    for i, item in enumerate(result):
                        print(f"   结果项 {i}: {type(item)} - {item}")
                        if hasattr(item, 'content'):
                            print(f"     内容: {item.content}")
                        if hasattr(item, 'text'):
                            print(f"     文本: {item.text}")
                        if i >= 2:  # 只显示前3个结果项
                            break
                            
            except Exception as e:
                print(f"   ❌ 工具调用失败: {e}")
                import traceback
                traceback.print_exc()
                
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("🚀 开始 FastMCP 包装器测试")
    print()
    
    await test_fastmcp_wrapper()
    await test_direct_fastmcp()
    
    print("\n🎉 所有测试完成")

if __name__ == "__main__":
    asyncio.run(main())
