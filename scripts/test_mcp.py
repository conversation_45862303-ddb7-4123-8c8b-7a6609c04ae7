#!/usr/bin/env python3
"""
MCP 系统测试脚本
用于验证 MCP 客户端和服务器是否正常工作
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# 添加后端路径到 Python 路径
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

from core.mcp_client import MC<PERSON>lient, MCPServerConfig, MCPTransportType

async def test_mcp_client():
    """测试 MCP 客户端"""
    print("🧪 开始测试 MCP 客户端...")
    
    # 创建测试客户端
    client = MCPClient()
    
    try:
        # 测试配置加载
        print("\n1. 测试配置加载...")
        success = await client.load_config()
        if success:
            print(f"✅ 配置加载成功，找到 {len(client.servers)} 个服务器")
            for name, server in client.servers.items():
                print(f"   - {name}: {server.transport_type.value}")
        else:
            print("⚠️  配置文件不存在或为空")
        
        # 测试服务器启动
        print("\n2. 测试服务器启动...")
        if client.servers:
            # 找到第一个未禁用的服务器
            test_server = None
            for name, server in client.servers.items():
                if not server.disabled:
                    test_server = name
                    break
            
            if test_server:
                print(f"   启动测试服务器: {test_server}")
                success = await client.start_server(test_server)
                if success:
                    print("✅ 服务器启动成功")
                    
                    # 测试工具发现
                    print("\n3. 测试工具发现...")
                    status = client.get_server_status(test_server)
                    if status.get('tools'):
                        print(f"✅ 发现 {len(status['tools'])} 个工具:")
                        for tool in status['tools']:
                            print(f"   - {tool['name']}: {tool['description']}")
                    else:
                        print("⚠️  未发现工具")
                    
                    # 测试工具调用
                    print("\n4. 测试工具调用...")
                    if client.tools:
                        tool_name = list(client.tools.keys())[0]
                        print(f"   调用工具: {tool_name}")
                        try:
                            result = await client.call_tool(tool_name, {"input": "test"})
                            print(f"✅ 工具调用成功: {result}")
                        except Exception as e:
                            print(f"❌ 工具调用失败: {e}")
                    else:
                        print("⚠️  没有可用工具")
                    
                    # 停止服务器
                    print(f"\n5. 停止服务器: {test_server}")
                    await client.stop_server(test_server)
                    print("✅ 服务器已停止")
                else:
                    print("❌ 服务器启动失败")
            else:
                print("⚠️  没有可用的测试服务器")
        else:
            print("⚠️  没有配置的服务器")
        
        print("\n🎉 MCP 客户端测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理
        await client.stop_all_servers()

async def test_config_creation():
    """测试配置文件创建"""
    print("\n📝 测试配置文件创建...")
    
    config_path = Path(".settings/mcp.json")
    
    if not config_path.exists():
        print("   创建默认配置文件...")
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        default_config = {
            "mcpServers": {
                "test-echo": {
                    "transport": "stdio",
                    "command": "python3",
                    "args": ["-c", "import sys, json; print(json.dumps({'result': 'echo test'}))"],
                    "env": {},
                    "disabled": False,
                    "autoApprove": [],
                    "timeout": 30,
                    "retryCount": 3
                }
            }
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置文件已创建: {config_path}")
    else:
        print(f"✅ 配置文件已存在: {config_path}")

def main():
    """主函数"""
    print("🚀 MCP 系统测试")
    print("=" * 50)
    
    # 检查 Python 版本
    if sys.version_info < (3, 7):
        print("❌ 需要 Python 3.7 或更高版本")
        return
    
    # 检查后端目录
    if not backend_path.exists():
        print(f"❌ 后端目录不存在: {backend_path}")
        return
    
    print(f"✅ Python 版本: {sys.version}")
    print(f"✅ 后端目录: {backend_path}")
    
    # 运行测试
    asyncio.run(test_config_creation())
    asyncio.run(test_mcp_client())

if __name__ == "__main__":
    main()