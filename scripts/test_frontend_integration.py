#!/usr/bin/env python3
"""
测试 FastMCP 客户端与前端 MCP 管理功能的整合
"""
import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 减少日志噪音
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_mcp_management_integration():
    """测试 MCP 管理功能整合"""
    print("🚀 测试 FastMCP 客户端与前端 MCP 管理功能的整合")
    print("请确保城市分级查询 MCP 服务器正在运行 (python mcp_server/city/server.py)")
    print("服务器应该运行在 http://localhost:8080/sse")
    print()
    
    try:
        # 测试导入
        print("1. 测试模块导入...")
        from backend.core.mcp import MCPClient, MCPTransportType, MCPServerConfig, mcp_client
        print("   ✅ 模块导入成功")
        
        # 测试全局客户端
        print("\n2. 测试全局客户端...")
        print(f"   全局客户端类型: {type(mcp_client)}")
        print(f"   全局客户端属性: {[attr for attr in dir(mcp_client) if not attr.startswith('_')]}")
        
        # 添加服务器配置
        print("\n3. 添加服务器配置...")
        server_config = MCPServerConfig(
            name="city_test",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:8080/sse",
            timeout=30,
            retry_count=3
        )
        
        mcp_client.add_server(server_config)
        print("   ✅ 服务器配置添加成功")
        
        # 测试服务器状态（未连接）
        print("\n4. 测试服务器状态（未连接）...")
        status = mcp_client.get_server_status("city_test")
        print(f"   状态: {status}")
        
        # 启动服务器
        print("\n5. 启动服务器...")
        success = await mcp_client.start_server("city_test")
        if not success:
            print("   ❌ 服务器启动失败")
            return
        print("   ✅ 服务器启动成功")
        
        # 测试服务器状态（已连接）
        print("\n6. 测试服务器状态（已连接）...")
        status = mcp_client.get_server_status("city_test")
        print(f"   状态: {status['status']}")
        print(f"   连接信息: {status.get('connection_info', 'N/A')}")
        print(f"   工具数量: {len(status.get('tools', []))}")
        
        # 测试获取所有服务器状态
        print("\n7. 测试获取所有服务器状态...")
        all_status = mcp_client.get_server_status()
        print(f"   服务器数量: {len(all_status)}")
        for name, status in all_status.items():
            print(f"   - {name}: {status['status']}")
        
        # 测试获取可用工具
        print("\n8. 测试获取可用工具...")
        tools = mcp_client.get_available_tools()
        print(f"   工具数量: {len(tools)}")
        for tool in tools:
            print(f"   - {tool['name']}: {tool['description'][:50]}...")
        
        # 测试兼容性属性
        print("\n9. 测试兼容性属性...")
        
        print("   9.1 connections 属性:")
        connections = mcp_client.connections
        for name, conn in connections.items():
            print(f"     - {name}: connected={conn['connected']}, type={conn['transport_type']}")
        
        print("   9.2 servers 属性:")
        servers = mcp_client.servers
        print(f"     服务器配置数量: {len(servers)}")
        
        print("   9.3 tools 属性:")
        tools_dict = mcp_client.tools
        print(f"     工具字典数量: {len(tools_dict)}")
        
        # 测试工具调用
        print("\n10. 测试工具调用...")
        try:
            result = await mcp_client.call_tool("get_city_tier", {"city_name": "深圳"})
            print("   ✅ 工具调用成功")
            print(f"   结果: {result}")
        except Exception as e:
            print(f"   ❌ 工具调用失败: {e}")
        
        # 测试批量操作
        print("\n11. 测试批量操作...")
        
        # 添加另一个服务器配置（模拟）
        server_config2 = MCPServerConfig(
            name="test_server_2",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:8080/sse",
            timeout=30,
            disabled=True  # 禁用状态
        )
        mcp_client.add_server(server_config2)
        
        # 测试启动所有服务器（只会启动未禁用的）
        print("   11.1 测试启动所有服务器...")
        await mcp_client.start_all_servers()
        print("   ✅ 批量启动完成")
        
        # 检查状态
        all_status = mcp_client.get_server_status()
        print(f"   活跃服务器数量: {sum(1 for s in all_status.values() if s['status'] == 'connected')}")
        
        # 测试停止所有服务器
        print("   11.2 测试停止所有服务器...")
        await mcp_client.stop_all_servers()
        print("   ✅ 批量停止完成")
        
        # 最终状态检查
        print("\n12. 最终状态检查...")
        final_status = mcp_client.get_server_status()
        for name, status in final_status.items():
            print(f"   - {name}: {status['status']}")
        
        print("\n✅ 所有测试通过！FastMCP 客户端与前端 MCP 管理功能整合成功")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

async def test_api_compatibility():
    """测试 API 兼容性"""
    print("\n🔧 测试 API 兼容性")
    print("=" * 50)
    
    try:
        from backend.core.mcp import mcp_client
        
        # 测试所有必需的方法和属性
        required_methods = [
            'add_server', 'start_server', 'stop_server', 
            'start_all_servers', 'stop_all_servers',
            'get_server_status', 'get_available_tools', 'call_tool'
        ]
        
        required_properties = ['connections', 'servers', 'tools']
        
        print("1. 检查必需的方法...")
        for method in required_methods:
            if hasattr(mcp_client, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} - 缺失")
                return False
        
        print("\n2. 检查必需的属性...")
        for prop in required_properties:
            if hasattr(mcp_client, prop):
                print(f"   ✅ {prop}")
            else:
                print(f"   ❌ {prop} - 缺失")
                return False
        
        print("\n✅ API 兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"\n❌ API 兼容性测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始 FastMCP 客户端前端整合测试")
    print()
    
    # 测试 API 兼容性
    api_ok = await test_api_compatibility()
    if not api_ok:
        print("\n❌ API 兼容性测试失败，停止测试")
        return
    
    # 测试 MCP 管理功能整合
    integration_ok = await test_mcp_management_integration()
    
    if api_ok and integration_ok:
        print("\n🎉 所有测试通过！")
        print("FastMCP 客户端已成功整合到前端 MCP 管理功能中")
    else:
        print("\n❌ 部分测试失败")

if __name__ == "__main__":
    asyncio.run(main())
