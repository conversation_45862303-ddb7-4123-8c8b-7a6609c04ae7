#!/usr/bin/env python3
"""
调试 MCP 工具调用
"""
import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.core.mcp_client import MCPServerConfig, MCPTransportType, mcp_client

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)

async def main():
    print("🚀 开始调试 MCP 工具调用")
    print("请确保城市分级查询 MCP 服务器正在运行 (python mcp_server/city/server.py)")
    print("服务器应该运行在 http://localhost:8080/sse")
    print()
    
    # 添加服务器配置
    config = MCPServerConfig(
        name="city",
        transport_type=MCPTransportType.SSE,
        url="http://localhost:8080/sse"
    )
    mcp_client.add_server(config)
    
    try:
        # 启动服务器
        print("1. 启动 MCP 服务器...")
        success = await mcp_client.start_server("city")
        if not success:
            print("❌ 服务器启动失败")
            return
        print("✅ 服务器启动成功")
        print()
        
        # 获取服务器状态
        print("2. 获取服务器状态...")
        status = mcp_client.get_server_status("city")
        print(f"状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        print()
        
        # 获取可用工具
        print("3. 获取可用工具...")
        tools = mcp_client.get_available_tools()
        print(f"发现 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        print()
        
        # 测试工具调用
        print("4. 测试工具调用...")
        tool_name = "get_city_tier"
        parameters = {"city_name": "北京"}
        
        print(f"测试工具: {tool_name}")
        print(f"参数: {parameters}")
        
        try:
            # 直接获取连接进行调试
            connection = mcp_client.connections["city"]
            print(f"连接类型: {type(connection)}")
            print(f"会话端点: {connection.session_endpoint}")
            
            # 构建请求
            request = {
                "jsonrpc": "2.0",
                "id": "debug123",
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }
            
            print(f"请求: {json.dumps(request, indent=2)}")
            
            # 手动发送请求
            if connection.session_endpoint:
                # 构建 POST 请求的 URL
                import urllib.parse
                parsed_base = urllib.parse.urlparse(connection.base_url)
                post_url = f"{parsed_base.scheme}://{parsed_base.netloc}{connection.session_endpoint}"
                
                print(f"POST URL: {post_url}")
                
                async with connection.session.post(
                    post_url,
                    json=request,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                ) as response:
                    print(f"POST 响应状态: {response.status}")
                    print(f"POST 响应头: {dict(response.headers)}")
                    
                    if response.status in [200, 202]:
                        # 对于 202 状态，我们需要检查 SSE 端点获取响应
                        if response.status == 202:
                            print("收到 202 Accepted，检查 SSE 端点获取响应")
                            # 等待一小段时间让服务器处理
                            await asyncio.sleep(0.5)
                            
                            # 使用相同的 ID 检查 SSE 端点
                            query_params = {
                                'method': 'tools/call',
                                'id': 'debug123'
                            }
                            query_string = urllib.parse.urlencode(query_params)
                            check_url = f"{connection.base_url}?{query_string}"
                            
                            print(f"检查 URL: {check_url}")
                            
                            async with connection.session.get(
                                check_url,
                                headers={
                                    "Accept": "text/event-stream",
                                    "Cache-Control": "no-cache"
                                },
                                timeout=30
                            ) as check_response:
                                print(f"检查响应状态: {check_response.status}")
                                print(f"检查响应头: {dict(check_response.headers)}")
                                
                                if check_response.status == 200:
                                    content_type = check_response.headers.get('content-type', '')
                                    print(f"检查响应 Content-Type: {content_type}")
                                    
                                    if 'text/event-stream' in content_type:
                                        print("开始处理 SSE 流...")
                                        # 手动处理 SSE 流
                                        async for line in check_response.content:
                                            line_str = line.decode('utf-8').strip()
                                            print(f"SSE 行: {line_str}")
                                            
                                            if line_str.startswith('data: '):
                                                data = line_str[6:]  # 去掉 'data: ' 前缀
                                                if data.strip():
                                                    try:
                                                        result = json.loads(data)
                                                        print(f"解析的 SSE 数据: {result}")
                                                    except json.JSONDecodeError as e:
                                                        print(f"JSON 解析失败: {e}, 数据: {data}")
                                    else:
                                        # 尝试直接解析响应
                                        try:
                                            result = await check_response.json()
                                            print(f"JSON 响应: {result}")
                                        except:
                                            text_response = await check_response.text()
                                            print(f"文本响应: {text_response}")
                                else:
                                    error_text = await check_response.text()
                                    print(f"检查失败: {check_response.status}, 响应: {error_text}")
                        else:
                            # 对于 200 状态，直接解析响应
                            try:
                                result = await response.json()
                                print(f"JSON 响应: {result}")
                            except:
                                text_response = await response.text()
                                print(f"文本响应: {text_response}")
                    else:
                        error_text = await response.text()
                        print(f"POST 请求失败: {response.status}, 响应: {error_text}")
            else:
                print("没有会话端点")
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            import traceback
            traceback.print_exc()
        
    finally:
        # 停止服务器
        print("\n5. 停止服务器...")
        await mcp_client.stop_server("city")
        print("✅ 服务器已停止")
    
    print("\n🎉 调试完成")

if __name__ == "__main__":
    asyncio.run(main())