#!/bin/bash

# MCP 系统安装脚本

echo "开始安装 MCP (Model Context Protocol) 系统..."

# 检查 Python 环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 Python 3，请先安装 Python 3"
    exit 1
fi

# 检查 uv 是否已安装
if ! command -v uv &> /dev/null; then
    echo "正在安装 uv Python 包管理器..."
    
    # 根据操作系统选择安装方式
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install uv
        else
            curl -LsSf https://astral.sh/uv/install.sh | sh
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl -LsSf https://astral.sh/uv/install.sh | sh
    else
        echo "请手动安装 uv: https://docs.astral.sh/uv/getting-started/installation/"
        exit 1
    fi
    
    # 重新加载 shell 配置
    source ~/.bashrc 2>/dev/null || source ~/.zshrc 2>/dev/null || true
fi

# 检查 uvx 是否可用
if ! command -v uvx &> /dev/null; then
    echo "错误: uvx 不可用，请确保 uv 正确安装"
    exit 1
fi

echo "✓ uv 和 uvx 已安装"

# 安装后端依赖
echo "正在安装后端 MCP 依赖..."
cd backend
pip install -r requirements.txt

echo "✓ 后端依赖已安装"

# 创建 MCP 配置目录
echo "正在创建 MCP 配置目录..."
mkdir -p .settings

# 如果配置文件不存在，创建默认配置
if [ ! -f ".settings/mcp.json" ]; then
    echo "正在创建默认 MCP 配置..."
    cat > .settings/mcp.json << 'EOF'
{
  "mcpServers": {
    "filesystem": {
      "command": "uvx",
      "args": ["mcp-server-filesystem@latest", "--path", "."],
      "env": {},
      "disabled": false,
      "autoApprove": ["read_file", "list_directory"]
    }
  }
}
EOF
    echo "✓ 默认 MCP 配置已创建"
else
    echo "✓ MCP 配置文件已存在"
fi

# 测试 MCP 服务器
echo "正在测试 MCP 服务器..."
echo "测试文件系统 MCP 服务器..."

# 尝试运行文件系统服务器进行测试
timeout 10s uvx mcp-server-filesystem@latest --path . --help > /dev/null 2>&1
if [ $? -eq 0 ] || [ $? -eq 124 ]; then
    echo "✓ 文件系统 MCP 服务器测试成功"
else
    echo "⚠ 文件系统 MCP 服务器测试失败，但这可能是正常的"
fi

echo ""
echo "🎉 MCP 系统安装完成！"
echo ""
echo "支持的传输方式："
echo "1. stdio - 标准输入输出 (推荐用于本地工具)"
echo "2. SSE - Server-Sent Events (适用于实时流式数据)"
echo "3. streamable-http - 流式HTTP (适用于RESTful API)"
echo ""
echo "可用的 MCP 服务器示例："
echo "1. 文件系统服务器 (stdio) - 已配置并启用"
echo "2. AWS 文档服务器 (stdio) - uvx awslabs.aws-documentation-mcp-server@latest"
echo "3. Git 服务器 (stdio) - uvx mcp-server-git@latest"
echo "4. SQLite 服务器 (stdio) - uvx mcp-server-sqlite@latest"
echo "5. 自定义 HTTP 服务器 - 配置 URL 端点"
echo ""
echo "使用方法："
echo "1. 启动应用程序"
echo "2. 进入设置 -> MCP服务"
echo "3. 配置和管理 MCP 服务器"
echo "4. 选择合适的传输方式"
echo ""
echo "配置文件位置: .settings/mcp.json"
echo "更多 MCP 服务器: https://github.com/modelcontextprotocol/servers"