#!/usr/bin/env python3
"""
测试 SSE MCP 服务器连接
专门用于测试与外部 MCP 服务器的连接
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path

# 添加后端路径
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

from core.mcp_client import MCPServerConfig, MCPTransportType, MCPSSEConnection

async def test_sse_server(url: str):
    """测试 SSE MCP 服务器"""
    print(f"🧪 测试 SSE MCP 服务器: {url}")
    print("=" * 50)
    
    # 创建配置
    config = MCPServerConfig(
        name="test-sse",
        transport_type=MCPTransportType.SSE,
        url=url,
        timeout=30
    )
    
    # 创建连接
    connection = MCPSSEConnection(config)
    
    try:
        # 1. 测试连接
        print("1. 测试连接...")
        success = await connection.connect()
        
        if success:
            print("✅ 连接成功")
            print(f"   基础URL: {connection.base_url}")
        else:
            print("❌ 连接失败")
            return
        
        # 2. 测试工具列表
        print("\n2. 获取工具列表...")
        try:
            response = await connection.send_request("tools/list")
            print(f"✅ 工具列表请求成功")
            print(f"   响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
            
            if "result" in response and "tools" in response["result"]:
                tools = response["result"]["tools"]
                print(f"   发现 {len(tools)} 个工具:")
                for tool in tools:
                    print(f"     - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
        except Exception as e:
            print(f"❌ 工具列表请求失败: {e}")
        
        # 3. 测试 ping
        print("\n3. 测试 ping...")
        try:
            response = await connection.send_request("ping")
            print(f"✅ Ping 成功: {response}")
        except Exception as e:
            print(f"⚠️  Ping 失败 (这可能是正常的): {e}")
        
        # 4. 如果有工具，测试调用第一个工具
        print("\n4. 测试工具调用...")
        try:
            # 先获取工具列表
            tools_response = await connection.send_request("tools/list")
            if "result" in tools_response and "tools" in tools_response["result"]:
                tools = tools_response["result"]["tools"]
                if tools:
                    first_tool = tools[0]
                    tool_name = first_tool["name"]
                    
                    print(f"   调用工具: {tool_name}")
                    
                    # 构建测试参数
                    test_params = {}
                    if "inputSchema" in first_tool:
                        schema = first_tool["inputSchema"]
                        if "properties" in schema:
                            for prop_name, prop_info in schema["properties"].items():
                                if prop_info.get("type") == "string":
                                    test_params[prop_name] = "test"
                                elif prop_info.get("type") == "number":
                                    test_params[prop_name] = 1
                                elif prop_info.get("type") == "boolean":
                                    test_params[prop_name] = True
                    
                    response = await connection.send_request("tools/call", {
                        "name": tool_name,
                        "arguments": test_params
                    })
                    
                    print(f"✅ 工具调用成功: {json.dumps(response, indent=2, ensure_ascii=False)}")
                else:
                    print("⚠️  没有可用工具")
            else:
                print("⚠️  无法获取工具列表")
                
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理连接
        await connection.disconnect()
        print("\n🧹 连接已清理")

async def manual_test(url: str):
    """手动测试连接"""
    print(f"🔧 手动测试连接: {url}")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 解析 URL
        import urllib.parse
        parsed = urllib.parse.urlparse(url)
        base_url = f"{parsed.scheme}://{parsed.netloc}"
        
        # 测试不同的端点
        test_endpoints = [
            f"{base_url}/",
            f"{base_url}/health",
            f"{base_url}/status",
            f"{url}",
            f"{url}/health",
        ]
        
        for endpoint in test_endpoints:
            try:
                print(f"测试端点: {endpoint}")
                async with session.get(endpoint, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    print(f"  状态码: {response.status}")
                    print(f"  Content-Type: {response.headers.get('content-type', 'Unknown')}")
                    
                    if response.status == 200:
                        try:
                            text = await response.text()
                            if len(text) < 500:  # 只显示短响应
                                print(f"  响应: {text}")
                            else:
                                print(f"  响应长度: {len(text)} 字符")
                        except:
                            print("  无法读取响应内容")
                    print()
                    
            except Exception as e:
                print(f"  错误: {e}")
                print()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_sse_connection.py <SSE_URL>")
        print("示例: python test_sse_connection.py http://localhost:8080/sse")
        return
    
    url = sys.argv[1]
    
    print("🚀 SSE MCP 服务器连接测试")
    print("=" * 50)
    print(f"目标URL: {url}")
    print()
    
    # 运行测试
    asyncio.run(manual_test(url))
    asyncio.run(test_sse_server(url))

if __name__ == "__main__":
    main()