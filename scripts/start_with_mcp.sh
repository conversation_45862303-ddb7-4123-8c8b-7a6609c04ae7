#!/bin/bash

# 启动带有 MCP 支持的应用程序

echo "🚀 启动会计应用 (带 MCP 支持)"
echo "================================"

# 检查依赖
echo "检查依赖..."

# 检查 Python
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到 Python 3"
    exit 1
fi

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 未找到 Node.js"
    exit 1
fi

# 检查 uv (可选)
if command -v uv &> /dev/null; then
    echo "✅ uv 已安装"
else
    echo "⚠️  uv 未安装，某些 MCP 服务器可能无法使用"
fi

echo "✅ 基本依赖检查完成"

# 创建 MCP 配置目录
cd backend
mkdir -p .settings

# 检查 MCP 配置
if [ ! -f ".settings/mcp.json" ]; then
    echo "📝 创建默认 MCP 配置..."
    cat > .settings/mcp.json << 'EOF'
{
  "mcpServers": {
    "filesystem-stdio": {
      "transport": "stdio",
      "command": "python3",
      "args": ["-c", "import sys, json, os; print(json.dumps({'jsonrpc': '2.0', 'result': {'tools': [{'name': 'list_files', 'description': '列出文件', 'inputSchema': {'type': 'object', 'properties': {'path': {'type': 'string'}}}}]}}))"],
      "env": {},
      "disabled": false,
      "autoApprove": ["list_files"],
      "timeout": 30,
      "retryCount": 3
    }
  }
}
EOF
    echo "✅ 默认 MCP 配置已创建"
fi

# 安装后端依赖
echo "📦 安装后端依赖..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    echo "✅ 后端依赖安装完成"
else
    echo "❌ 未找到 requirements.txt"
    exit 1
fi
cd ..

# 安装前端依赖
echo "📦 安装前端依赖..."
cd frontend
if [ -f "package.json" ]; then
    if command -v yarn &> /dev/null; then
        yarn install
    else
        npm install
    fi
    echo "✅ 前端依赖安装完成"
else
    echo "❌ 未找到 package.json"
    exit 1
fi
cd ..

# 测试 MCP 系统
echo "🧪 测试 MCP 系统..."
python3 scripts/test_mcp.py

# 启动应用
echo "🚀 启动应用程序..."

# 启动后端
echo "启动后端服务器..."
cd backend
python3 main.py &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 3

# 启动前端
echo "启动前端开发服务器..."
cd frontend
if command -v yarn &> /dev/null; then
    yarn dev &
else
    npm run dev &
fi
FRONTEND_PID=$!
cd ..

echo ""
echo "🎉 应用程序启动完成！"
echo ""
echo "📍 访问地址:"
echo "   前端: http://localhost:5173"
echo "   后端: http://localhost:8000"
echo "   MCP 管理: http://localhost:5173 -> 设置 -> MCP服务"
echo ""
echo "🛑 停止应用: Ctrl+C"
echo ""

# 等待用户中断
trap 'echo "正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
wait