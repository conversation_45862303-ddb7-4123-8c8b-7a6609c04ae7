#!/usr/bin/env python3
"""
测试更新后的基于 FastMCP 的 MCP 客户端
"""
import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from backend.core.mcp import MCPClient, MCPTransportType, MCPServerConfig

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_updated_mcp_client():
    """测试更新后的 MCP 客户端"""
    print("🚀 测试更新后的基于 FastMCP 的 MCP 客户端")
    print("请确保城市分级查询 MCP 服务器正在运行 (python mcp_server/city/server.py)")
    print("服务器应该运行在 http://localhost:8080/sse")
    print()
    
    # 创建 MCP 客户端
    client = MCPClient()
    
    # 添加服务器配置
    server_config = MCPServerConfig(
        name="city",
        transport_type=MCPTransportType.SSE,
        url="http://localhost:8080/sse",
        timeout=30,
        retry_count=3
    )
    
    client.add_server(server_config)
    
    try:
        # 启动服务器
        print("1. 启动 MCP 服务器...")
        success = await client.start_server("city")
        if not success:
            print("❌ 服务器启动失败")
            return
        
        print("✅ 服务器启动成功")
        
        # 获取服务器状态
        print("\n2. 获取服务器状态...")
        status = client.get_server_status("city")
        print("状态:", json.dumps(status, indent=2, ensure_ascii=False, default=str))
        
        # 获取可用工具
        print("\n3. 获取可用工具...")
        tools = client.get_available_tools()
        print(f"发现 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description'][:100]}...")
        
        # 测试工具调用
        print("\n4. 测试工具调用...")
        
        # 测试 get_city_tier
        print("4.1 测试 get_city_tier...")
        try:
            result = await client.call_tool("get_city_tier", {"city_name": "北京"})
            print("✅ get_city_tier 调用成功")
            print("结果:", json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ get_city_tier 调用失败: {e}")
        
        # 测试 search_cities
        print("\n4.2 测试 search_cities...")
        try:
            result = await client.call_tool("search_cities", {"keyword": "北"})
            print("✅ search_cities 调用成功")
            print("结果:", json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ search_cities 调用失败: {e}")
        
        # 测试 get_all_tiers
        print("\n4.3 测试 get_all_tiers...")
        try:
            result = await client.call_tool("get_all_tiers", {})
            print("✅ get_all_tiers 调用成功")
            print("结果:", json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ get_all_tiers 调用失败: {e}")
        
        # 测试 list_cities_by_tier
        print("\n4.4 测试 list_cities_by_tier...")
        try:
            result = await client.call_tool("list_cities_by_tier", {"tier": "一线城市"})
            print("✅ list_cities_by_tier 调用成功")
            print("结果:", json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ list_cities_by_tier 调用失败: {e}")
        
        # 测试兼容性属性
        print("\n5. 测试兼容性属性...")
        print("5.1 连接信息:")
        connections = client.connections
        print(json.dumps(connections, indent=2, ensure_ascii=False, default=str))
        
        print("\n5.2 服务器配置:")
        servers = client.servers
        print(f"服务器数量: {len(servers)}")
        for name, config in servers.items():
            print(f"  - {name}: {config.url}")
        
        print("\n5.3 工具信息:")
        tools_dict = client.tools
        print(f"工具数量: {len(tools_dict)}")
        for name, tool in tools_dict.items():
            print(f"  - {name}: {tool.description[:50]}...")
        
    finally:
        # 停止服务器
        print("\n6. 停止服务器...")
        await client.stop_server("city")
        print("✅ 服务器已停止")
    
    print("\n🎉 测试完成")

async def test_global_client():
    """测试全局客户端实例"""
    print("\n🌐 测试全局客户端实例")
    print("=" * 50)
    
    from backend.core.mcp import mcp_client
    
    # 添加服务器配置
    server_config = MCPServerConfig(
        name="city_global",
        transport_type=MCPTransportType.SSE,
        url="http://localhost:8080/sse",
        timeout=30,
        retry_count=3
    )
    
    mcp_client.add_server(server_config)
    
    try:
        # 启动服务器
        print("1. 启动全局 MCP 服务器...")
        success = await mcp_client.start_server("city_global")
        if not success:
            print("❌ 全局服务器启动失败")
            return
        
        print("✅ 全局服务器启动成功")
        
        # 测试工具调用
        print("\n2. 测试全局客户端工具调用...")
        try:
            result = await mcp_client.call_tool("get_city_tier", {"city_name": "上海"})
            print("✅ 全局客户端工具调用成功")
            print("结果:", json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ 全局客户端工具调用失败: {e}")
        
    finally:
        # 停止服务器
        print("\n3. 停止全局服务器...")
        await mcp_client.stop_server("city_global")
        print("✅ 全局服务器已停止")

async def main():
    """主函数"""
    print("🚀 开始测试更新后的 MCP 客户端")
    print()
    
    await test_updated_mcp_client()
    await test_global_client()
    
    print("\n🎉 所有测试完成")

if __name__ == "__main__":
    asyncio.run(main())
