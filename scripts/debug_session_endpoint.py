#!/usr/bin/env python3
"""
调试 MCP 会话端点检测
"""
import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.core.mcp_client import MCPServerConfig, MCPTransportType, mcp_client

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)

async def main():
    print("🚀 开始调试 MCP 会话端点检测")
    print("请确保城市分级查询 MCP 服务器正在运行 (python mcp_server/city/server.py)")
    print("服务器应该运行在 http://localhost:8080/sse")
    print()
    
    # 添加服务器配置
    config = MCPServerConfig(
        name="city",
        transport_type=MCPTransportType.SSE,
        url="http://localhost:8080/sse"
    )
    mcp_client.add_server(config)
    
    try:
        # 启动服务器
        print("1. 启动 MCP 服务器...")
        success = await mcp_client.start_server("city")
        if not success:
            print("❌ 服务器启动失败")
            return
        print("✅ 服务器启动成功")
        print()
        
        # 获取连接
        connection = mcp_client.connections["city"]
        print(f"连接类型: {type(connection)}")
        print(f"连接状态: {connection.connected}")
        print(f"基础 URL: {connection.base_url}")
        print(f"会话端点: {connection.session_endpoint}")
        print()
        
        # 手动发送 tools/list 请求以查看 SSE 流
        print("2. 手动发送 tools/list 请求...")
        import urllib.parse
        
        # 构建请求参数
        query_params = {
            'method': 'tools/list',
            'id': 'debug123'
        }
        query_string = urllib.parse.urlencode(query_params)
        sse_url = f"{connection.base_url}?{query_string}"
        
        print(f"SSE URL: {sse_url}")
        
        async with connection.session.get(
            sse_url,
            headers={
                "Accept": "text/event-stream",
                "Cache-Control": "no-cache"
            },
            timeout=30
        ) as response:
            print(f"响应状态: {response.status}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status == 200:
                content_type = response.headers.get('content-type', '')
                print(f"响应 Content-Type: {content_type}")
                
                if 'text/event-stream' in content_type:
                    print("开始处理 SSE 流...")
                    # 手动处理 SSE 流
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        print(f"SSE 行: {line_str}")
                        
                        if line_str.startswith('event: '):
                            event_type = line_str[7:]
                            print(f"事件类型: {event_type}")
                            
                        elif line_str.startswith('data: '):
                            data = line_str[6:]  # 去掉 'data: ' 前缀
                            if data.strip():
                                try:
                                    result = json.loads(data)
                                    print(f"解析的 SSE 数据: {result}")
                                except json.JSONDecodeError as e:
                                    print(f"JSON 解析失败: {e}, 数据: {data}")
                        
                        elif line_str.startswith('id: '):
                            event_id = line_str[4:]
                            print(f"事件ID: {event_id}")
                        
                        # 处理 ping 事件
                        elif line_str.startswith(': ping'):
                            print("收到 ping 事件")
                            continue
                else:
                    # 尝试直接解析响应
                    try:
                        result = await response.json()
                        print(f"JSON 响应: {result}")
                    except:
                        text_response = await response.text()
                        print(f"文本响应: {text_response}")
            else:
                error_text = await response.text()
                print(f"请求失败: {response.status}, 响应: {error_text}")
        
        print("\n3. 再次检查会话端点...")
        print(f"会话端点: {connection.session_endpoint}")
        
    finally:
        # 停止服务器
        print("\n4. 停止服务器...")
        await mcp_client.stop_server("city")
        print("✅ 服务器已停止")
    
    print("\n🎉 调试完成")

if __name__ == "__main__":
    asyncio.run(main())