#!/usr/bin/env python3
"""
调试 FastMCP 服务器的响应格式
"""

import asyncio
import aiohttp
import json
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def debug_fastmcp():
    """调试 FastMCP 服务器"""
    print("🔍 调试 FastMCP 服务器响应")
    
    base_url = "http://localhost:8080"
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试 SSE 端点
        print("\n1. 测试 SSE 端点...")
        sse_url = f"{base_url}/sse"
        
        try:
            # 测试工具列表请求
            params = {
                'method': 'tools/list',
                'id': 'debug123'
            }
            
            from urllib.parse import urlencode
            query_string = urlencode(params)
            test_url = f"{sse_url}?{query_string}"
            
            print(f"请求 URL: {test_url}")
            
            async with session.get(
                test_url,
                headers={
                    "Accept": "text/event-stream",
                    "Cache-Control": "no-cache"
                }
            ) as response:
                print(f"响应状态: {response.status}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    print(f"Content-Type: {content_type}")
                    
                    endpoint_url = None
                    lines_read = 0
                    
                    async for line in response.content:
                        if lines_read >= 10:  # 只读取前10行
                            break
                        line_str = line.decode('utf-8').strip()
                        print(f"  SSE 行 {lines_read + 1}: {line_str}")
                        
                        if line_str.startswith('event: endpoint'):
                            # 下一行应该是 data
                            pass
                        elif line_str.startswith('data: '):
                            data = line_str[6:]
                            if not endpoint_url:
                                endpoint_url = data
                                print(f"  🎯 发现端点 URL: {endpoint_url}")
                        
                        lines_read += 1
                    
                    # 2. 使用发现的端点进行实际请求
                    if endpoint_url:
                        print("\n2. 使用发现的端点进行工具列表请求...")
                        
                        # 构建完整的端点 URL
                        endpoint_full_url = f"{base_url}{endpoint_url}"
                        print(f"端点完整 URL: {endpoint_full_url}")
                        
                        # 尝试不同的请求方式
                        session_params = {
                            'method': 'tools/list',
                            'id': 'debug456'
                        }
                        session_query = urlencode(session_params)
                        session_url = f"{endpoint_full_url}?{session_query}"
                        
                        print(f"会话请求 URL: {session_url}")
                        
                        # 尝试方式1: 使用 SSE 头
                        print("\n  尝试方式1: 使用 SSE 头...")
                        try:
                            async with session.get(
                                session_url,
                                headers={
                                    "Accept": "text/event-stream",
                                    "Cache-Control": "no-cache"
                                }
                            ) as session_response:
                                print(f"  响应状态: {session_response.status}")
                                if session_response.status != 200:
                                    error_text = await session_response.text()
                                    print(f"  ❌ 失败: {error_text}")
                                else:
                                    print("  ✅ 成功！")
                        except Exception as e:
                            print(f"  ❌ 异常: {e}")
                        
                        # 尝试方式2: 不使用特殊头
                        print("\n  尝试方式2: 不使用特殊头...")
                        try:
                            async with session.get(session_url) as session_response:
                                print(f"  响应状态: {session_response.status}")
                                if session_response.status != 200:
                                    error_text = await session_response.text()
                                    print(f"  ❌ 失败: {error_text}")
                                else:
                                    print("  ✅ 成功！")
                                    # 尝试解析响应
                                    try:
                                        json_data = await session_response.json()
                                        print(f"  JSON 响应:")
                                        print(json.dumps(json_data, indent=2, ensure_ascii=False))
                                    except:
                                        text_data = await session_response.text()
                                        print(f"  文本响应:")
                                        print(text_data[:500])
                        except Exception as e:
                            print(f"  ❌ 异常: {e}")
                        
                        # 尝试方式3: 使用 POST 请求
                        print("\n  尝试方式3: 使用 POST 请求...")
                        try:
                            post_data = {
                                "jsonrpc": "2.0",
                                "id": "debug789",
                                "method": "tools/list",
                                "params": {}
                            }
                            async with session.post(
                                endpoint_full_url,
                                json=post_data,
                                headers={"Content-Type": "application/json"}
                            ) as session_response:
                                print(f"  响应状态: {session_response.status}")
                                print(f"  响应头: {dict(session_response.headers)}")
                                
                                if session_response.status in [200, 202]:
                                    print("  ✅ 请求被接受！")
                                    # 尝试解析响应
                                    try:
                                        json_data = await session_response.json()
                                        print(f"  JSON 响应:")
                                        print(json.dumps(json_data, indent=2, ensure_ascii=False))
                                    except:
                                        text_data = await session_response.text()
                                        print(f"  文本响应:")
                                        print(text_data[:500])
                                        
                                    # 如果是 202，可能需要检查 SSE 端点获取响应
                                    if session_response.status == 202:
                                        print("  🔄 检查 SSE 端点是否有响应...")
                                        check_url = f"{sse_url}&method=tools/list&id=debug789"
                                        async with session.get(check_url) as check_response:
                                            if check_response.status == 200:
                                                check_text = await check_response.text()
                                                print(f"  SSE 检查响应:")
                                                print(check_text[:500])
                                else:
                                    error_text = await session_response.text()
                                    print(f"  ❌ 失败: {error_text}")
                        except Exception as e:
                            print(f"  ❌ 异常: {e}")
                        
                        # 尝试方式4: 直接使用原始 SSE 端点，但使用不同的方法
                        print("\n  尝试方式4: 直接使用原始 SSE 端点，但使用不同的方法...")
                        try:
                            # 直接使用原始 SSE 端点，但这次等待更长时间
                            direct_url = f"{sse_url}&method=tools/list&id=direct123"
                            print(f"  直接请求 URL: {direct_url}")
                            
                            async with session.get(
                                direct_url,
                                headers={
                                    "Accept": "text/event-stream",
                                    "Cache-Control": "no-cache"
                                },
                                timeout=aiohttp.ClientTimeout(total=30)  # 增加超时时间
                            ) as direct_response:
                                print(f"  响应状态: {direct_response.status}")
                                
                                if direct_response.status == 200:
                                    print("  ✅ 直接请求成功！")
                                    # 读取更多行以寻找实际的工具列表响应
                                    line_count = 0
                                    found_data = False
                                    
                                    async for line in direct_response.content:
                                        if line_count >= 20:  # 读取更多行
                                            break
                                            
                                        line_str = line.decode('utf-8').strip()
                                        print(f"  直接 SSE 行 {line_count + 1}: {line_str}")
                                        
                                        if line_str.startswith('data: '):
                                            data = line_str[6:]
                                            if data.strip() and data != '[DONE]':
                                                try:
                                                    json_data = json.loads(data)
                                                    print(f"  ✅ 找到 JSON 数据:")
                                                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
                                                    found_data = True
                                                except:
                                                    print(f"  ❌ 无法解析 JSON: {data}")
                                        
                                        line_count += 1
                                    
                                    if not found_data:
                                        print("  ⚠️  未找到有效的工具列表数据")
                                else:
                                    error_text = await direct_response.text()
                                    print(f"  ❌ 失败: {error_text}")
                        except Exception as e:
                            print(f"  ❌ 异常: {e}")
                            print(f"会话响应状态: {session_response.status}")
                            print(f"会话响应头: {dict(session_response.headers)}")
                            
                            if session_response.status == 200:
                                session_content_type = session_response.headers.get('content-type', '')
                                print(f"会话 Content-Type: {session_content_type}")
                                
                                # 尝试解析响应
                                try:
                                    # 尝试作为 JSON 解析
                                    json_data = await session_response.json()
                                    print(f"✅ 成功解析 JSON 响应:")
                                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
                                except:
                                    # 尝试作为 SSE 流处理
                                    print("尝试作为 SSE 流处理...")
                                    session_lines = 0
                                    async for session_line in session_response.content:
                                        if session_lines >= 10:
                                            break
                                        session_line_str = session_line.decode('utf-8').strip()
                                        print(f"  会话 SSE 行 {session_lines + 1}: {session_line_str}")
                                        
                                        if session_line_str.startswith('data: '):
                                            session_data = session_line_str[6:]
                                            try:
                                                session_json = json.loads(session_data)
                                                print(f"  ✅ 解析到 JSON 数据:")
                                                print(json.dumps(session_json, indent=2, ensure_ascii=False))
                                            except:
                                                print(f"  ❌ 无法解析 JSON: {session_data}")
                                        
                                        session_lines += 1
                            else:
                                error_text = await session_response.text()
                                print(f"❌ 会话请求失败: {error_text}")
                else:
                    error_text = await response.text()
                    print(f"❌ SSE 请求失败: {error_text}")
                    
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {e}")
            logger.exception("详细错误信息:")

if __name__ == "__main__":
    asyncio.run(debug_fastmcp())