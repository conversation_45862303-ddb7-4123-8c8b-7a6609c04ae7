#!/usr/bin/env python3
"""
调试 MCP 服务器连接
专门用于调试和测试 MCP 服务器的各种端点
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path

async def debug_server_endpoints(base_url: str):
    """调试服务器的所有可能端点"""
    print(f"🔍 调试服务器端点: {base_url}")
    print("=" * 60)
    
    # 可能的端点列表
    endpoints_to_test = [
        # 健康检查端点
        ("/", "GET", "根路径"),
        ("/health", "GET", "健康检查"),
        ("/status", "GET", "状态检查"),
        ("/ping", "GET", "Ping"),
        
        # 消息端点
        ("/message", "POST", "消息端点"),
        ("/api/message", "POST", "API消息端点"),
        ("/rpc", "POST", "RPC端点"),
        ("/jsonrpc", "POST", "JSON-RPC端点"),
        
        # SSE 特定端点
        ("/sse", "GET", "SSE根路径"),
        ("/sse", "POST", "SSE POST"),
        ("/sse/message", "POST", "SSE消息端点"),
        ("/sse/stream", "GET", "SSE流端点"),
    ]
    
    async with aiohttp.ClientSession() as session:
        for path, method, description in endpoints_to_test:
            url = f"{base_url.rstrip('/')}{path}"
            
            try:
                print(f"\n🧪 测试: {method} {url}")
                print(f"   描述: {description}")
                
                if method == "GET":
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                        await handle_response(response, url)
                        
                elif method == "POST":
                    # 发送测试 JSON-RPC 请求
                    test_data = {
                        "jsonrpc": "2.0",
                        "id": "test",
                        "method": "ping"
                    }
                    
                    async with session.post(
                        url, 
                        json=test_data,
                        headers={"Content-Type": "application/json"},
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        await handle_response(response, url)
                        
            except asyncio.TimeoutError:
                print(f"   ⏰ 超时")
            except Exception as e:
                print(f"   ❌ 错误: {e}")

async def handle_response(response, url):
    """处理响应"""
    print(f"   📊 状态码: {response.status}")
    print(f"   📋 Content-Type: {response.headers.get('content-type', 'Unknown')}")
    
    # 显示其他有用的头部
    interesting_headers = ['server', 'x-powered-by', 'access-control-allow-origin']
    for header in interesting_headers:
        if header in response.headers:
            print(f"   📋 {header}: {response.headers[header]}")
    
    try:
        if response.status == 200:
            content_type = response.headers.get('content-type', '')
            
            if 'application/json' in content_type:
                data = await response.json()
                print(f"   ✅ JSON响应: {json.dumps(data, indent=6, ensure_ascii=False)}")
            elif 'text/event-stream' in content_type:
                print(f"   🌊 SSE流响应:")
                # 读取前几行 SSE 数据
                count = 0
                async for line in response.content:
                    if count >= 5:  # 只读取前5行
                        break
                    line_str = line.decode('utf-8').strip()
                    if line_str:
                        print(f"      {line_str}")
                        count += 1
            else:
                text = await response.text()
                if len(text) < 200:
                    print(f"   📄 文本响应: {text}")
                else:
                    print(f"   📄 文本响应 ({len(text)} 字符): {text[:100]}...")
                    
        elif response.status == 404:
            print(f"   🚫 端点不存在")
        elif response.status == 405:
            print(f"   🚫 方法不允许")
            # 检查允许的方法
            if 'allow' in response.headers:
                print(f"   📋 允许的方法: {response.headers['allow']}")
        else:
            text = await response.text()
            if text and len(text) < 200:
                print(f"   ⚠️  响应: {text}")
                
    except Exception as e:
        print(f"   ❌ 读取响应失败: {e}")

async def test_mcp_protocol(base_url: str):
    """测试 MCP 协议"""
    print(f"\n🔬 测试 MCP 协议: {base_url}")
    print("=" * 60)
    
    # 常见的 MCP 方法
    mcp_methods = [
        "initialize",
        "tools/list", 
        "tools/call",
        "resources/list",
        "ping",
        "capabilities"
    ]
    
    # 可能的消息端点
    message_endpoints = [
        "/message",
        "/api/message", 
        "/rpc",
        "/jsonrpc",
        "/sse/message"
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint in message_endpoints:
            url = f"{base_url.rstrip('/')}{endpoint}"
            
            print(f"\n📡 测试端点: {url}")
            
            for method in mcp_methods:
                try:
                    request_data = {
                        "jsonrpc": "2.0",
                        "id": f"test_{method.replace('/', '_')}",
                        "method": method
                    }
                    
                    # 为某些方法添加参数
                    if method == "tools/call":
                        request_data["params"] = {
                            "name": "test_tool",
                            "arguments": {}
                        }
                    elif method == "initialize":
                        request_data["params"] = {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {},
                            "clientInfo": {
                                "name": "test-client",
                                "version": "1.0.0"
                            }
                        }
                    
                    print(f"   🔧 方法: {method}")
                    
                    async with session.post(
                        url,
                        json=request_data,
                        headers={"Content-Type": "application/json"},
                        timeout=aiohttp.ClientTimeout(total=10)
                    ) as response:
                        
                        print(f"      状态: {response.status}")
                        
                        if response.status == 200:
                            try:
                                data = await response.json()
                                if "result" in data:
                                    print(f"      ✅ 成功: {json.dumps(data['result'], ensure_ascii=False)[:100]}...")
                                elif "error" in data:
                                    print(f"      ⚠️  错误: {data['error']}")
                                else:
                                    print(f"      📄 响应: {json.dumps(data, ensure_ascii=False)[:100]}...")
                            except:
                                text = await response.text()
                                print(f"      📄 文本: {text[:100]}...")
                        else:
                            print(f"      ❌ 失败")
                            
                except Exception as e:
                    print(f"      ❌ 异常: {e}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python debug_mcp_server.py <BASE_URL>")
        print("示例: python debug_mcp_server.py http://localhost:8080")
        return
    
    base_url = sys.argv[1].rstrip('/')
    
    print("🚀 MCP 服务器调试工具")
    print("=" * 60)
    print(f"目标服务器: {base_url}")
    
    # 运行调试
    asyncio.run(debug_server_endpoints(base_url))
    asyncio.run(test_mcp_protocol(base_url))
    
    print("\n🎯 调试建议:")
    print("1. 查看哪些端点返回 200 状态码")
    print("2. 注意 405 错误 - 表示端点存在但方法不对")
    print("3. 检查 Content-Type 头部确定响应格式")
    print("4. 对于 SSE，寻找 'text/event-stream' 响应")

if __name__ == "__main__":
    main()