#!/usr/bin/env python3
"""
调试 MCP 工具调用过程的详细脚本
"""
import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from backend.core.mcp import MCPClient, MCPTransportType, MCPServerConfig, MCPTool, MCPServerStatus

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_mcp_tool_call():
    """测试 MCP 工具调用"""
    print("🚀 开始详细测试 MCP 工具调用过程")
    print("请确保城市分级查询 MCP 服务器正在运行 (python mcp_server/city/server.py)")
    print("服务器应该运行在 http://localhost:8080/sse")
    print()
    
    # 创建 MCP 客户端
    client = MCPClient()
    
    # 添加服务器配置
    server_config = MCPServerConfig(
        name="city",
        transport_type=MCPTransportType.SSE,
        url="http://localhost:8080/sse",
        timeout=30,
        retry_count=3
    )
    
    client.add_server(server_config)
    
    try:
        # 启动服务器
        print("1. 启动 MCP 服务器...")
        success = await client.start_server("city")
        if not success:
            print("❌ 服务器启动失败")
            return
        
        print("✅ 服务器启动成功")
        
        # 获取服务器状态
        print("\n2. 获取服务器状态...")
        status = client.get_server_status("city")
        print("状态:", json.dumps(status, indent=2, ensure_ascii=False))
        
        # 获取可用工具
        print("\n3. 获取可用工具...")
        tools = client.get_available_tools()
        print(f"发现 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 测试工具调用
        print("\n4. 测试工具调用...")
        tool_name = "get_city_tier"
        parameters = {"city_name": "北京"}
        
        print(f"测试工具: {tool_name}")
        print(f"参数: {parameters}")
        
        # 详细检查连接和会话端点
        connection = client.connections.get("city")
        if connection:
            print(f"\n连接类型: {type(connection).__name__}")
            print(f"基础 URL: {getattr(connection, 'base_url', 'N/A')}")
            print(f"会话端点: {getattr(connection, 'session_endpoint', 'N/A')}")
            print(f"已初始化: {getattr(connection, '_initialized', 'N/A')}")
        
        # 调用工具
        try:
            result = await client.call_tool(tool_name, parameters)
            print("✅ 工具调用成功")
            print("结果:", json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            import traceback
            traceback.print_exc()
        
    finally:
        # 停止服务器
        print("\n5. 停止服务器...")
        await client.stop_server("city")
        print("✅ 服务器已停止")
    
    print("\n🎉 测试完成")

if __name__ == "__main__":
    asyncio.run(test_mcp_tool_call())