#!/usr/bin/env python3
"""
测试城市分级 MCP 服务器
"""

import asyncio
import sys
from pathlib import Path

# 添加后端路径
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

from core.mcp_client import MC<PERSON>lient, MCPServerConfig, MCPTransportType

async def test_city_tier_server():
    """测试城市分级服务器"""
    print("🏙️  测试城市分级 MCP 服务器")
    print("=" * 50)
    
    # 创建客户端
    client = MCPClient()
    
    # 创建城市分级服务器配置
    config = MCPServerConfig(
        name="city-tier",
        transport_type=MCPTransportType.SSE,
        url="http://localhost:8080/sse",
        timeout=30,
        retry_count=3
    )
    
    client.servers["city-tier"] = config
    
    try:
        # 启动服务器
        print("1. 启动服务器...")
        success = await client.start_server("city-tier")
        
        if success:
            print("✅ 服务器启动成功")
            
            # 获取状态
            status = client.get_server_status("city-tier")
            print(f"   状态: {status}")
            
            # 测试工具发现
            print("\n2. 测试工具发现...")
            if client.tools:
                print(f"✅ 发现 {len(client.tools)} 个工具:")
                for tool_name, tool in client.tools.items():
                    print(f"   - {tool_name}: {tool.description}")
                
                # 测试工具调用
                print("\n3. 测试工具调用...")
                first_tool = list(client.tools.keys())[0]
                try:
                    result = await client.call_tool(first_tool, {"city": "北京"})
                    print(f"✅ 工具调用成功: {result}")
                except Exception as e:
                    print(f"❌ 工具调用失败: {e}")
            else:
                print("⚠️  未发现工具")
        else:
            print("❌ 服务器启动失败")
            status = client.get_server_status("city-tier")
            if status.get('error_message'):
                print(f"   错误: {status['error_message']}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        await client.stop_all_servers()
        print("\n🧹 测试完成")

if __name__ == "__main__":
    asyncio.run(test_city_tier_server())