#!/usr/bin/env python3
"""
测试修复后的 MCP 客户端与 FastMCP 服务器的连接
"""

import asyncio
import sys
import os
import json
import logging

# 添加后端路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from core.mcp_client import MCPClient, MCPServerConfig, MCPTransportType

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_mcp_connection():
    """测试 MCP 连接"""
    print("🧪 测试修复后的 MCP 客户端连接")
    
    # 创建 MCP 客户端
    client = MCPClient()
    
    # 添加城市分级查询服务器配置
    city_server_config = MCPServerConfig(
        name="city",
        transport_type=MCPTransportType.SSE,
        url="http://localhost:8080/sse",
        timeout=30,
        retry_count=3
    )
    
    client.servers["city"] = city_server_config
    
    try:
        # 启动服务器
        print("\n1. 启动 MCP 服务器...")
        success = await client.start_server("city")
        
        if success:
            print("✅ 服务器启动成功")
            
            # 获取服务器状态
            print("\n2. 获取服务器状态...")
            status = client.get_server_status("city")
            # 创建一个可序列化的副本
            serializable_status = {}
            for key, value in status.items():
                if key == 'transport_type':
                    serializable_status[key] = str(value)
                elif key == 'tools' and isinstance(value, list):
                    # 处理工具列表
                    tools_list = []
                    for tool in value:
                        tool_dict = {}
                        for t_key, t_value in tool.items():
                            if t_key == 'server_name':
                                tool_dict[t_key] = t_value
                            else:
                                tool_dict[t_key] = t_value
                        tools_list.append(tool_dict)
                    serializable_status[key] = tools_list
                else:
                    serializable_status[key] = value
            print(f"状态: {json.dumps(serializable_status, indent=2, ensure_ascii=False)}")
            
            # 获取可用工具
            print("\n3. 获取可用工具...")
            tools = client.get_available_tools()
            print(f"发现 {len(tools)} 个工具:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
            
            # 测试工具调用
            if tools:
                print("\n4. 测试工具调用...")
                tool_name = tools[0]['name']
                print(f"测试工具: {tool_name}")
                
                try:
                    if tool_name == "get_city_tier":
                        result = await client.call_tool(tool_name, {"city_name": "上海"})
                        print(f"工具调用结果: {result}")
                    elif tool_name == "list_cities_by_tier":
                        result = await client.call_tool(tool_name, {"tier": "一线城市"})
                        print(f"工具调用结果: {result}")
                    elif tool_name == "get_all_tiers":
                        result = await client.call_tool(tool_name, {})
                        print(f"工具调用结果: {result}")
                    elif tool_name == "search_cities":
                        result = await client.call_tool(tool_name, {"keyword": "州"})
                        print(f"工具调用结果: {result}")
                    else:
                        print(f"未知工具: {tool_name}")
                        
                except Exception as e:
                    print(f"❌ 工具调用失败: {e}")
            
            print("\n✅ 所有测试完成")
            
        else:
            print("❌ 服务器启动失败")
            status = client.get_server_status("city")
            print(f"错误信息: {status.get('error_message', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        logger.exception("详细错误信息:")
        
    finally:
        # 停止服务器
        print("\n5. 停止服务器...")
        await client.stop_server("city")
        print("✅ 服务器已停止")

async def test_direct_sse():
    """直接测试 SSE 连接"""
    print("\n🧪 直接测试 SSE 连接")
    
    import aiohttp
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试 SSE 端点
            sse_url = "http://localhost:8080/sse"
            print(f"测试 SSE 端点: {sse_url}")
            
            # 测试工具列表请求
            params = {
                'method': 'tools/list',
                'id': 'test123'
            }
            
            from urllib.parse import urlencode
            query_string = urlencode(params)
            test_url = f"{sse_url}?{query_string}"
            
            print(f"请求 URL: {test_url}")
            
            async with session.get(
                test_url,
                headers={
                    "Accept": "text/event-stream",
                    "Cache-Control": "no-cache"
                }
            ) as response:
                print(f"响应状态: {response.status}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    print(f"Content-Type: {content_type}")
                    
                    if 'text/event-stream' in content_type:
                        print("✅ 收到 SSE 流")
                        # 读取前几行
                        line_count = 0
                        async for line in response.content:
                            if line_count >= 5:  # 只读取前5行
                                break
                            line_str = line.decode('utf-8').strip()
                            print(f"  {line_str}")
                            line_count += 1
                    else:
                        # 尝试作为 JSON 处理
                        try:
                            data = await response.json()
                            print(f"✅ 收到 JSON 响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        except:
                            text = await response.text()
                            print(f"✅ 收到文本响应: {text[:500]}...")
                else:
                    text = await response.text()
                    print(f"❌ 请求失败: {text}")
                    
        except Exception as e:
            print(f"❌ SSE 测试失败: {e}")
            logger.exception("详细错误信息:")

if __name__ == "__main__":
    print("🚀 开始测试 MCP 客户端修复")
    print("请确保城市分级查询 MCP 服务器正在运行 (python mcp_server/city/server.py)")
    print("服务器应该运行在 http://localhost:8080/sse")
    print()
    
    # 运行测试
    asyncio.run(test_direct_sse())
    asyncio.run(test_mcp_connection())
    
    print("\n🎉 测试完成")