#!/usr/bin/env python3
"""
测试 FastMCP 客户端 API
了解 FastMCP 的正确使用方法
"""
import asyncio
import sys
import os
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_fastmcp_import():
    """测试 FastMCP 导入和基本 API"""
    print("🧪 测试 FastMCP 客户端 API")
    print("=" * 50)
    
    try:
        # 尝试导入 FastMCP
        print("1. 导入 FastMCP...")
        try:
            import fastmcp
            print(f"   ✅ FastMCP 版本: {getattr(fastmcp, '__version__', 'unknown')}")
            print(f"   可用属性: {dir(fastmcp)}")
        except ImportError as e:
            print(f"   ❌ 导入 FastMCP 失败: {e}")
            return
        
        # 查看客户端类
        print("\n2. 查看客户端类...")
        if hasattr(fastmcp, 'FastMCPClient'):
            print("   ✅ 找到 FastMCPClient")
            client_class = fastmcp.FastMCPClient
            print(f"   客户端方法: {[m for m in dir(client_class) if not m.startswith('_')]}")
        elif hasattr(fastmcp, 'Client'):
            print("   ✅ 找到 Client")
            client_class = fastmcp.Client
            print(f"   客户端方法: {[m for m in dir(client_class) if not m.startswith('_')]}")
        else:
            print("   ❌ 未找到客户端类")
            print(f"   可用类: {[attr for attr in dir(fastmcp) if not attr.startswith('_')]}")
            return
        
        # 尝试创建客户端
        print("\n3. 创建客户端...")
        try:
            # 尝试不同的构造方法
            if hasattr(fastmcp, 'FastMCPClient'):
                client = fastmcp.FastMCPClient("http://localhost:8080/sse")
            elif hasattr(fastmcp, 'Client'):
                client = fastmcp.Client("http://localhost:8080/sse")
            else:
                print("   ❌ 无法确定客户端类")
                return
            
            print(f"   ✅ 客户端创建成功: {type(client)}")
            print(f"   客户端实例方法: {[m for m in dir(client) if not m.startswith('_')]}")
            
        except Exception as e:
            print(f"   ❌ 创建客户端失败: {e}")
            return
        
        # 测试连接
        print("\n4. 测试连接...")
        try:
            if hasattr(client, 'connect'):
                await client.connect()
                print("   ✅ 连接成功")
            else:
                print("   ⚠️ 客户端没有 connect 方法")
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
            # 继续测试其他方法
        
        # 测试工具列表
        print("\n5. 测试工具列表...")
        try:
            if hasattr(client, 'list_tools'):
                tools = await client.list_tools()
                print(f"   ✅ 获取工具列表成功: {tools}")
            elif hasattr(client, 'get_tools'):
                tools = await client.get_tools()
                print(f"   ✅ 获取工具列表成功: {tools}")
            else:
                print("   ⚠️ 客户端没有工具列表方法")
        except Exception as e:
            print(f"   ❌ 获取工具列表失败: {e}")
        
        # 测试工具调用
        print("\n6. 测试工具调用...")
        try:
            if hasattr(client, 'call_tool'):
                result = await client.call_tool("get_city_tier", {"city_name": "北京"})
                print(f"   ✅ 工具调用成功: {result}")
            elif hasattr(client, 'invoke_tool'):
                result = await client.invoke_tool("get_city_tier", {"city_name": "北京"})
                print(f"   ✅ 工具调用成功: {result}")
            else:
                print("   ⚠️ 客户端没有工具调用方法")
        except Exception as e:
            print(f"   ❌ 工具调用失败: {e}")
        
        # 清理
        print("\n7. 清理连接...")
        try:
            if hasattr(client, 'disconnect'):
                await client.disconnect()
                print("   ✅ 断开连接成功")
            elif hasattr(client, 'close'):
                await client.close()
                print("   ✅ 关闭连接成功")
            else:
                print("   ⚠️ 客户端没有断开连接方法")
        except Exception as e:
            print(f"   ❌ 断开连接失败: {e}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

async def test_manual_http_client():
    """手动测试 HTTP 客户端"""
    print("\n🔧 手动测试 HTTP 客户端")
    print("=" * 50)
    
    import aiohttp
    import json
    
    base_url = "http://localhost:8080"
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. 测试 SSE 端点获取会话
            print("1. 获取 SSE 会话端点...")
            async with session.get(f"{base_url}/sse") as response:
                if response.status == 200:
                    print(f"   ✅ SSE 端点响应: {response.status}")
                    
                    # 读取端点信息
                    session_endpoint = None
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        print(f"   SSE 行: {line_str}")
                        
                        if line_str.startswith('data: '):
                            endpoint_path = line_str[6:]
                            if endpoint_path.startswith('/'):
                                session_endpoint = endpoint_path
                                print(f"   ✅ 获取到会话端点: {endpoint_path}")
                                break
                        
                        # 只读取前几行
                        break
                    
                    if session_endpoint:
                        # 2. 使用会话端点发送初始化请求
                        print("\n2. 发送初始化请求...")
                        session_url = f"{base_url}{session_endpoint}"
                        
                        init_request = {
                            "jsonrpc": "2.0",
                            "id": "init_test",
                            "method": "initialize",
                            "params": {
                                "protocolVersion": "2024-11-05",
                                "capabilities": {"sampling": {}},
                                "clientInfo": {"name": "test-client", "version": "1.0.0"}
                            }
                        }
                        
                        async with session.post(
                            session_url,
                            json=init_request,
                            headers={"Content-Type": "application/json"}
                        ) as init_response:
                            print(f"   初始化响应: {init_response.status}")
                            if init_response.status in [200, 202]:
                                print("   ✅ 初始化成功")
                                
                                # 3. 发送工具调用请求
                                print("\n3. 发送工具调用请求...")
                                tool_request = {
                                    "jsonrpc": "2.0",
                                    "id": "tool_test",
                                    "method": "tools/call",
                                    "params": {
                                        "name": "get_city_tier",
                                        "arguments": {"city_name": "北京"}
                                    }
                                }
                                
                                async with session.post(
                                    session_url,
                                    json=tool_request,
                                    headers={"Content-Type": "application/json"}
                                ) as tool_response:
                                    print(f"   工具调用响应: {tool_response.status}")
                                    
                                    if tool_response.status == 200:
                                        result = await tool_response.json()
                                        print(f"   ✅ 工具调用成功: {result}")
                                    elif tool_response.status == 202:
                                        print("   ⏳ 工具调用已接受，需要通过 SSE 获取结果")
                                        
                                        # 4. 通过 SSE 获取结果
                                        print("\n4. 通过 SSE 获取结果...")
                                        async with session.get(
                                            session_url,
                                            headers={
                                                "Accept": "text/event-stream",
                                                "Cache-Control": "no-cache"
                                            }
                                        ) as sse_response:
                                            if sse_response.status == 200:
                                                print("   ✅ SSE 连接成功，等待结果...")
                                                
                                                line_count = 0
                                                async for line in sse_response.content:
                                                    line_str = line.decode('utf-8').strip()
                                                    if line_str:
                                                        print(f"   SSE 结果行: {line_str}")
                                                        line_count += 1
                                                        
                                                        if line_str.startswith('data: '):
                                                            data = line_str[6:]
                                                            try:
                                                                result = json.loads(data)
                                                                if isinstance(result, dict) and ('result' in result or 'error' in result):
                                                                    print(f"   ✅ 获取到工具调用结果: {result}")
                                                                    break
                                                            except json.JSONDecodeError:
                                                                pass
                                                    
                                                    # 限制读取行数
                                                    if line_count > 10:
                                                        print("   ⏹️ 达到读取限制")
                                                        break
                                            else:
                                                print(f"   ❌ SSE 连接失败: {sse_response.status}")
                                    else:
                                        error_text = await tool_response.text()
                                        print(f"   ❌ 工具调用失败: {error_text}")
                            else:
                                error_text = await init_response.text()
                                print(f"   ❌ 初始化失败: {error_text}")
                    else:
                        print("   ❌ 未获取到会话端点")
                else:
                    print(f"   ❌ SSE 端点失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ 手动测试失败: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """主函数"""
    print("🚀 开始 FastMCP API 测试")
    print("请确保 MCP 服务器正在运行在 http://localhost:8080/sse")
    print()
    
    await test_fastmcp_import()
    await test_manual_http_client()
    
    print("\n🎉 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
