#!/usr/bin/env python3
"""
快速测试 SSE MCP 服务器
"""

import asyncio
import aiohttp
import json

async def quick_test():
    """快速测试"""
    base_url = "http://localhost:8080"
    
    print("🧪 快速测试 MCP 服务器")
    print(f"服务器: {base_url}")
    
    async with aiohttp.ClientSession() as session:
        # 测试根路径
        try:
            async with session.get(f"{base_url}/") as response:
                print(f"GET / : {response.status}")
                if response.status == 200:
                    text = await response.text()
                    print(f"响应: {text[:200]}...")
        except Exception as e:
            print(f"GET / 失败: {e}")
        
        # 测试消息端点
        test_request = {
            "jsonrpc": "2.0",
            "id": "test",
            "method": "tools/list"
        }
        
        endpoints = ["/message", "/api/message", "/rpc"]
        
        for endpoint in endpoints:
            try:
                url = f"{base_url}{endpoint}"
                async with session.post(url, json=test_request) as response:
                    print(f"POST {endpoint} : {response.status}")
                    if response.status == 200:
                        data = await response.json()
                        print(f"响应: {json.dumps(data, ensure_ascii=False)[:200]}...")
            except Exception as e:
                print(f"POST {endpoint} 失败: {e}")

if __name__ == "__main__":
    asyncio.run(quick_test())