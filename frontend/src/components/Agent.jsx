import { useState, useRef, useEffect, useCallback } from 'react';
import {
  Send, Upload, Image, File, FileText,
  RotateCcw, StopCircle, CheckCircle, Edit,
  MessageSquare, Loader, AlertCircle, MessageCircle
} from 'lucide-react';
import SubjectCard from './SubjectCard';
import AssetCard from './AssetCard';
import StaffCard from './StaffCard';
import VoucherCard from './VoucherCard';
import SimpleChatInput from './SimpleChatInput';
import api, { backendBase } from '../api'; // 引入 backendBase

// 消息类型枚举
const MESSAGE_TYPES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
  FILE: 'file',
  CARD: 'card',
  ERROR: 'error',
  THINKING: 'thinking',
  ANALYSIS: 'analysis',
  CREATING_CARDS: 'creating_cards',
  INFO: 'info',
};

// Toast组件
const Toast = ({ message, type = 'success', onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 3000);
    return () => clearTimeout(timer);
  }, [onClose]);

  const bgColor = type === 'error' ? 'bg-red-500' : type === 'info' ? 'bg-blue-500' : 'bg-green-500';
  const icon = type === 'error' ? <AlertCircle size={20} /> : type === 'info' ? <MessageCircle size={20} /> : <CheckCircle size={20} />;

  return (
    <div className={`fixed top-6 right-6 z-50 ${bgColor} text-white px-4 py-2 rounded shadow flex items-center animate-bounce-in`}>
      {icon}
      <span className="ml-2">{message}</span>
    </div>
  );
};

// 工具函数：提取AI回复中的json代码块内容
function extractJsonFromCodeBlock(content) {
  // 匹配```json:xxx```或```json\nxxx```或```json\r\nxxx```等
  const match = content.match(/```json[:\n\r]*([\s\S]*?)```/i);
  if (match) {
    return match[1].trim();
  }
  // 兼容形如json: {...} 但无代码块
  const match2 = content.match(/json[:\n\r]*([\s\S]*)/i);
  if (match2) {
    try {
      // 尝试解析json
      const jsonStr = match2[1].trim();
      JSON.parse(jsonStr);
      return jsonStr;
    } catch {
      // 不是标准json，原样返回
      return match2[1].trim();
    }
  }
  return content;
}

// 工具函数：清理文本中的JSON格式化内容，返回纯净的用户友好文本
function cleanJsonFromText(content) {
  if (!content || typeof content !== 'string') return content || '';
  
  let cleaned = content;
  
  // 1. 移除JSON代码块格式（```json ... ```）
  cleaned = cleaned.replace(/```json[:\n\r]*[\s\S]*?```/gi, '');
  
  // 2. 移除独立的JSON对象格式（可能出现在流式响应中）
  // 匹配类似 {"key": "value"} 或 {"key": "value", "key2": "value2"} 的格式
  cleaned = cleaned.replace(/\{[\s\S]*?\}/g, '');
  
  // 3. 移除JSON键值对格式（如 "audit_conclusion": "需要更多信息"）
  cleaned = cleaned.replace(/"[^"]*"\s*:\s*"[^"]*"/g, '');
  
  // 4. 移除多余的逗号、引号和括号
  cleaned = cleaned.replace(/[,{}[\]]/g, '');
  
  // 5. 移除多余的空行和空白字符
  cleaned = cleaned.replace(/\n\s*\n/g, '\n');
  cleaned = cleaned.replace(/^\s+|\s+$/g, '');
  
  // 6. 移除可能的 "json:" 前缀
  cleaned = cleaned.replace(/^json[:\s]*/i, '');
  
  // 7. 如果清理后内容为空，返回原始内容的非JSON部分
  if (!cleaned.trim()) {
    // 尝试提取原始内容中的非JSON文本
    const nonJsonParts = content.split(/\{[\s\S]*?\}/).filter(part => part.trim());
    if (nonJsonParts.length > 0) {
      cleaned = nonJsonParts.join(' ');
    } else {
      cleaned = content; // 如果所有尝试都失败，返回原始内容
    }
  }
  
  return cleaned.trim();
}

// 消息组件
const Message = ({ message, onRetry, handleCardConfirm, sendMessageWithContext }) => {
  const [showPreview, setShowPreview] = useState(false);

  // 支持 ESC 关闭预览弹窗
  useEffect(() => {
    if (!showPreview) return;
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') setShowPreview(false);
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showPreview]);


  // 文件预览弹窗
  const renderPreviewModal = () => {
    if (!message.fileUrl) return null;
    if (message.fileType === 'image') {
      return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4" onClick={() => setShowPreview(false)}>
          <div className="relative max-w-[95vw] max-h-[95vh] flex items-center justify-center">
            <img
              src={message.fileUrl}
              alt={message.fileName}
              className="max-w-full max-h-full object-contain rounded-xl shadow-2xl"
              onClick={e => e.stopPropagation()}
            />
            <button
              onClick={() => setShowPreview(false)}
              className="absolute top-4 right-4 w-10 h-10 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200"
            >
              ×
            </button>
          </div>
        </div>
      );
    }
    if (message.fileType === 'pdf') {
      return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4" onClick={() => setShowPreview(false)}>
          <div className="bg-white rounded-2xl shadow-2xl max-w-[95vw] max-h-[95vh] p-6 flex flex-col" onClick={e => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">{message.fileName}</h3>
              <button
                onClick={() => setShowPreview(false)}
                className="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full flex items-center justify-center transition-all duration-200"
              >
                ×
              </button>
            </div>
            <iframe
              src={message.fileUrl}
              title={message.fileName}
              className="flex-1 w-full border rounded-xl"
              style={{ minHeight: '70vh' }}
            />
          </div>
        </div>
      );
    }
    return null;
  };

  const renderContent = () => {
    switch (message.type) {
      case MESSAGE_TYPES.USER:
        return (
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-2xl max-w-[80%] ml-auto shadow-lg">
            <div className="leading-relaxed">{message.content}</div>
          </div>
        );
      case MESSAGE_TYPES.ASSISTANT:
        // 新增：自动提取json代码块
        const displayContent = extractJsonFromCodeBlock(message.content);
        return (
          <div className="bg-white border border-gray-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="flex items-start justify-between">
              <div className="flex-1 whitespace-pre-wrap text-gray-800 leading-relaxed">{displayContent}</div>
              {message.error && (
                <button
                  onClick={() => onRetry(message.id)}
                  className="ml-3 text-red-500 hover:text-red-700 hover:bg-red-50 p-1.5 rounded-lg transition-all duration-200"
                  title="重试"
                >
                  <RotateCcw size={16} />
                </button>
              )}
            </div>
          </div>
        );
      case MESSAGE_TYPES.FILE:
        return (
          <div className="flex justify-end">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-2xl max-w-xs shadow-lg flex items-center gap-3">
              {message.fileType === 'image' && message.fileUrl && (
                <img
                  src={message.fileUrl}
                  alt={message.fileName}
                  className="w-12 h-12 object-cover rounded-lg cursor-pointer border border-white/20"
                  onClick={() => setShowPreview(true)}
                />
              )}
              {message.fileType === 'pdf' && (
                <div className="flex items-center gap-2">
                  <FileText size={16} className="text-white" />
                  <span
                    className="font-medium text-white cursor-pointer hover:underline text-sm truncate"
                    onClick={() => message.fileUrl && setShowPreview(true)}
                  >
                    {message.fileName}
                  </span>
                </div>
              )}
              {!message.fileUrl && (
                <div className="flex items-center gap-2">
                  {message.fileType === 'image' ? (
                    <Image size={16} className="text-white" />
                  ) : (
                    <FileText size={16} className="text-white" />
                  )}
                  <span className="text-white text-sm truncate">{message.fileName}</span>
                </div>
              )}
              {message.processing && <Loader className="animate-spin text-white" size={16} />}
              {message.error && <AlertCircle className="text-red-300" size={16} />}
              {showPreview && renderPreviewModal()}
            </div>
          </div>
        );
      case MESSAGE_TYPES.CARD:
        return (
          <div className="space-y-4 max-w-full">
            {message.cards && message.cards.map((card, index) => {
              if (card.skip) return null;

              // 合并props，优先级：props > data > card
              const cardProps = {
                ...(card.data || {}),
                ...(card.props || {}),
                editable: card._pending,
                onConfirm: card._pending ? (data) => handleCardConfirm(card, message.id, index) : undefined,
                type: card.type,
              };

              // 直接渲染卡片组件，不添加额外的包装容器
              if (card.type === 'subject') return <SubjectCard key={index} {...cardProps} />;
              if (card.type === 'asset') return <AssetCard key={index} {...cardProps} />;
              if (card.type === 'staff') return <StaffCard key={index} {...cardProps} />;
              if (card.type === 'voucher') return <VoucherCard key={index} {...cardProps} />;
              return null;
            })}
          </div>
        );
      case MESSAGE_TYPES.ERROR:
        return (
          <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 p-4 rounded-2xl max-w-[80%] shadow-soft overflow-hidden">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-red-500 rounded-xl text-white shadow-lg">
                <AlertCircle size={16} />
              </div>
              <div>
                <div className="font-semibold text-red-800 mb-1">发生错误</div>
                <div
                  className="text-red-700 leading-relaxed overflow-hidden"
                  style={{
                    wordBreak: 'break-all',
                    overflowWrap: 'break-word',
                    hyphens: 'auto',
                    maxWidth: '100%',
                    whiteSpace: 'pre-wrap'
                  }}
                >
                  {message.content}
                </div>
              </div>
            </div>
          </div>
        );
      case MESSAGE_TYPES.THINKING:
        return (
          <div className="flex items-center gap-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="relative">
              <div className="w-6 h-6 border-2 border-blue-200 rounded-full animate-spin">
                <div className="absolute inset-0 border-2 border-transparent border-t-blue-500 border-r-purple-500 rounded-full animate-spin"></div>
              </div>
            </div>
            <span className="text-gray-700 font-medium">智能体思考中<span className="loading-dots"></span></span>
          </div>
        );
      case MESSAGE_TYPES.ANALYSIS:
        return (
          <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="text-gray-800 whitespace-pre-wrap leading-relaxed">{message.content}</div>
          </div>
        );
      case MESSAGE_TYPES.CREATING_CARDS:
        return (
          <div className="flex items-center gap-2 bg-purple-50 p-3 rounded-lg max-w-[80%]">
            <Loader className="animate-spin text-purple-400" size={20} />
            <span className="text-purple-700">正在创建卡片...</span>
          </div>
        );
      case MESSAGE_TYPES.SYSTEM:
        return (
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">系统消息</span>
            </div>
            <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">{message.content}</div>
          </div>
        );
      case MESSAGE_TYPES.INFO:
        return (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className="text-xs font-medium text-blue-500 uppercase tracking-wide">提示信息</span>
            </div>
            <div className="text-blue-800 whitespace-pre-wrap leading-relaxed">{message.content}</div>
          </div>
        );
      default:
        return <div className="p-3">{message.content}</div>;
    }
  };

  // 判断是否为用户消息（包括文本消息和文件消息）
  const isUserMessage = message.type === MESSAGE_TYPES.USER || message.type === MESSAGE_TYPES.FILE;

  return (
    <div className="mb-4">
      <div className={`flex items-start gap-2 ${isUserMessage ? 'justify-end' : 'justify-start'}`}>
        <div className={`flex-1 ${isUserMessage ? 'flex justify-end' : ''}`}>
          {renderContent()}
        </div>
      </div>
      <div className={`text-xs text-gray-500 mt-1 px-3 ${isUserMessage ? 'text-right' : 'text-left'}`}>
        {new Date(message.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
};

// 文件上传组件
const FileUpload = ({ onFileSelect, multiple = false, accept = "image/*,.pdf" }) => {
  const fileInputRef = useRef(null);
  const [dragOver, setDragOver] = useState(false);

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    onFileSelect(multiple ? files : files[0]);
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    onFileSelect(multiple ? files : files[0]);
  };

  return (
    <div
      className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${dragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
        }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => fileInputRef.current?.click()}
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple={multiple}
        accept={accept}
        onChange={handleFileChange}
        className="hidden"
      />
      <Upload className="mx-auto mb-2 text-gray-400" size={24} />
      <p className="text-gray-600">
        {multiple ? '拖拽文件到这里或点击选择多个文件' : '拖拽文件到这里或点击选择文件'}
      </p>
      <p className="text-sm text-gray-500 mt-1">
        支持图片 (PNG, JPG, BMP, TIFF, WEBP) 和 PDF 文件
      </p>
    </div>
  );
};

// 主组件
const Agent = ({
  setVouchers, setSubjects, setAssets, setStaffs,
  aiConfig, inSidebar = false, session, updateSession
}) => {
  const [messages, setMessages] = useState(session?.messages || []);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState(session?.id || null);
  const [toast, setToast] = useState(null);
  // 移除 showFileUpload
  // const [showFileUpload, setShowFileUpload] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [abortController, setAbortController] = useState(null);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [selectedFunction, setSelectedFunction] = useState('单据审核'); // 新增功能选择状态

  const fileInputRef = useRef(null);
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const streamingMessageRef = useRef(null);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 显示Toast
  const showToast = (message, type = 'success') => {
    setToast({ message, type });
  };

  // 处理卡片数据（只渲染到消息区，确认后才加入工作区）
  const processCards = (cards) => {
    console.log('[DEBUG] processCards called, cards:', cards);
    if (!cards) return;
    // 只渲染未 skip 的卡片（支持skip: true或op: 'skip'都跳过）
    const validCards = cards.filter(card => !card.skip && card.op !== 'skip');
    if (validCards.length === 0) return;
    // 新增一条CARD类型消息，内容为卡片数组
    addMessage('', MESSAGE_TYPES.CARD, {
      cards: validCards.map(card => ({
        ...card,
        // 标记未确认
        _pending: true
      }))
    });
  };

  // 卡片确认后加入工作区
  const handleCardConfirm = (card, messageId, cardIdx) => {
    // 加入对应工作区
    if (card.type === 'subject') setSubjects(prev => [...prev, card.data || card.props || card]);
    if (card.type === 'asset') setAssets(prev => [...prev, card.data || card.props || card]);
    if (card.type === 'staff') setStaffs(prev => [...prev, card.data || card.props || card]);
    if (card.type === 'voucher') setVouchers(prev => [...prev, card.data || card.props || card]);
    // 消息区该卡片标记为已确认
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId && msg.type === MESSAGE_TYPES.CARD && msg.cards) {
        const newCards = msg.cards.map((c, idx) => idx === cardIdx ? { ...c, _pending: false } : c);
        return { ...msg, cards: newCards };
      }
      return msg;
    }));
  };

  // 添加消息
  const addMessage = (content, type = MESSAGE_TYPES.ASSISTANT, extra = {}) => {
    const message = {
      id: Date.now() + Math.random(),
      content,
      type,
      timestamp: new Date(),
      ...extra
    };
    setMessages(prev => [...prev, message]);
    return message;
  };

  // 更新消息
  const updateMessage = (id, updates) => {
    setMessages(prev => prev.map(msg =>
      msg.id === id ? { ...msg, ...updates } : msg
    ));
  };

  // 配置智能体
  const configureAgent = async () => {
    try {
      const response = await fetch(`${backendBase}/agent/v2/configure`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        },
        body: JSON.stringify({
          session_id: sessionId
        })
      });

      const result = await response.json();
      if (result.success) {
        setSessionId(result.session_id);
        return true;
      } else {
        throw new Error(result.error || '配置失败');
      }
    } catch (error) {
      console.error('配置智能体失败:', error);
      showToast(error.message, 'error');
      return false;
    }
  };

  // 新增：多轮自洽处理
  const [context, setContext] = useState({});
  const [autoLooping, setAutoLooping] = useState(false);
  const autoLoopRef = useRef(false);

  // action 处理主函数
  const handleAgentAction = async (response, userInput) => {
    try {
      console.log('[DEBUG] handleAgentAction called', response);
      const { action, cards, answer, data, is_finished } = response;
      // 新增：只要有 answer 就渲染
      if (answer) {
        addMessage(answer, MESSAGE_TYPES.ASSISTANT);
      }
      switch (action) {
        case 'create_subject':
          await api.createSubject(data);
          showToast('科目创建成功');
          break;
        case 'update_asset':
          await api.updateAsset(data);
          showToast('资产信息已更新');
          break;
        case 'delete_staff':
          await api.deleteStaff(data);
          showToast('员工已删除');
          break;
        case 'query_supplier':
          const supplier = await api.querySupplier(data);
          showToast('供应商信息已查询');
          break;
        case 'create_voucher':
        case 'create_card':
          if (cards && cards.length > 0) {
            processCards(cards);
          }
          break;
        case 'answer_question':
          // answer 已展示，无需重复
          break;
        case 'fetch_subjects':
          const subjects = await api.getSubjects();
          setContext(prev => ({ ...prev, subjects }));
          await sendMessageWithContext(userInput, { ...context, subjects });
          break;
        case 'none':
          showToast('操作已完成');
          break;
        default:
          showToast('暂不支持该操作');
      }
      if (!is_finished) {
        // 你可以自动继续下一轮，或等待用户输入
      }
    } catch (error) {
      console.error('handleAgentAction error:', error);
      // 清除思考消息
      setMessages(prev => prev.filter(msg =>
        msg.type !== MESSAGE_TYPES.THINKING &&
        msg.type !== MESSAGE_TYPES.ANALYSIS &&
        msg.type !== MESSAGE_TYPES.CREATING_CARDS
      ));
      addMessage(`处理响应时发生错误: ${error.message}`, MESSAGE_TYPES.ERROR);
      showToast('处理响应失败', 'error');
    }
  };

  // 封装带上下文的 sendMessage
  const sendMessageWithContext = async (content, extraContext = {}) => {
    if ((!content.trim() && !(extraContext.files && extraContext.files.length > 0)) || isLoading) return;
    // 配置智能体
    const configured = await configureAgent();
    if (!configured) return;
    // 添加用户消息
    if (content && content.trim()) {
      addMessage(content, MESSAGE_TYPES.USER);
    } else if (extraContext.files && extraContext.files.length > 0) {
      addMessage('用户上传了文件', MESSAGE_TYPES.USER);
    }
    setInput('');
    setIsLoading(true);
    setIsStreaming(true);
    // 插入思考动画
    const thinkingMsg = addMessage('', MESSAGE_TYPES.THINKING);
    let thinkingMsgId = thinkingMsg.id;
    let analysisMsgId = null;
    let creatingCardsMsgId = null;
    let gotCards = false;
    let gotAnalysis = false;
    let lastAnalysis = '';
    let fullBuffer = '';
    let jsonCandidate = '';
    const controller = new AbortController();
    setAbortController(controller);
    try {
      const response = await fetch(`${backendBase}/agent/v2/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        },
        body: JSON.stringify({
          content,
          session_id: sessionId,
          user_id: 'user',
          use_enhanced: true,
          files: extraContext.files || null,
          ...extraContext
        }),
        signal: controller.signal
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        fullBuffer += chunk;
        jsonCandidate += chunk;
        try {
          const matches = jsonCandidate.match(/\{[\s\S]*\}/g);
          if (matches && matches.length > 0) {
            const last = matches[matches.length - 1];
            const result = JSON.parse(last);
            if (result.analysis && result.analysis !== lastAnalysis) {
              lastAnalysis = result.analysis;
              gotAnalysis = true;
              if (!analysisMsgId) {
                setMessages(prev => prev.filter(msg => msg.id !== thinkingMsgId));
                const analysisMsg = addMessage(result.analysis, MESSAGE_TYPES.ANALYSIS);
                analysisMsgId = analysisMsg.id;
              } else {
                updateMessage(analysisMsgId, { content: lastAnalysis });
              }
            }
            // 修正：流式中间阶段不处理 cards 字段，不渲染卡片
          }
        } catch (e) {
          // 不是完整JSON，忽略
        }
      }
      if (gotAnalysis && !gotCards) {
        const newThinkingMsg = addMessage('', MESSAGE_TYPES.THINKING);
        thinkingMsgId = newThinkingMsg.id;
      }
      if (!fullBuffer.trim()) {
        // 清除思考消息
        setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
        addMessage('❌ 没有收到响应内容', MESSAGE_TYPES.ERROR);
      } else {
        try {
          const result = JSON.parse(fullBuffer);
          if (result.success === false) {
            // 清除思考消息
            setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
            addMessage(`❌ ${result.error}`, MESSAGE_TYPES.ERROR);
          } else {
            // 移除所有思考动画
            setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
            // 只在流式结束后统一处理 answer/card/action
            try {
              await handleAgentAction(result, content); // 只在这里处理 cards
            } catch (error) {
              console.error('handleAgentAction failed:', error);
              // 清除思考消息
              setMessages(prev => prev.filter(msg =>
                msg.type !== MESSAGE_TYPES.THINKING &&
                msg.type !== MESSAGE_TYPES.ANALYSIS &&
                msg.type !== MESSAGE_TYPES.CREATING_CARDS
              ));
              addMessage(`处理AI响应时发生错误: ${error.message}`, MESSAGE_TYPES.ERROR);
            }
          }
        } catch (e) {
          // 清除思考消息
          setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
          if (analysisMsgId) {
            updateMessage(analysisMsgId, { content: fullBuffer });
          }
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        addMessage(`发送失败: ${error.message}`, MESSAGE_TYPES.ERROR);
        showToast('发送消息失败', 'error');
      } else {
        // 如果是用户主动停止，清除思考消息
        setMessages(prev => prev.filter(msg =>
          msg.type !== MESSAGE_TYPES.THINKING &&
          msg.type !== MESSAGE_TYPES.ANALYSIS &&
          msg.type !== MESSAGE_TYPES.CREATING_CARDS
        ));
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
      streamingMessageRef.current = null;
      setAutoLooping(false);
      autoLoopRef.current = false;
    }
  };

  // 封装单据审核模式的 sendMessage
  const sendAuditRequestWithContext = async (content, extraContext = {}) => {
    if ((!content.trim() && !(extraContext.files && extraContext.files.length > 0)) || isLoading) return;
    // 配置智能体
    const configured = await configureAgent();
    if (!configured) return;
    // 添加用户消息
    if (content && content.trim()) {
      addMessage(content, MESSAGE_TYPES.USER);
    } else if (extraContext.files && extraContext.files.length > 0) {
      addMessage('用户上传了文件，请进行单据审核', MESSAGE_TYPES.USER);
    }
    setInput('');
    setIsLoading(true);
    setIsStreaming(true);
    // 插入思考动画
    const thinkingMsg = addMessage('', MESSAGE_TYPES.THINKING);
    let thinkingMsgId = thinkingMsg.id;
    let analysisMsgId = null;
    let creatingCardsMsgId = null;
    let gotCards = false;
    let gotAnalysis = false;
    let lastAnalysis = '';
    let fullBuffer = '';
    let jsonCandidate = '';
    const controller = new AbortController();
    setAbortController(controller);
    try {
      const response = await fetch(`${backendBase}/agent/v2/audit/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        },
        body: JSON.stringify({
          content,
          session_id: sessionId,
          user_id: 'user',
          use_enhanced: true,
          files: extraContext.files || null,
          ...extraContext
        }),
        signal: controller.signal
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let isStreamingText = true; // 标记是否正在流式传输文本内容
      let structuredResponse = null; // 存储结构化响应
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        fullBuffer += chunk;
        
        // 检查是否是结构化JSON响应的开始
        if (chunk.trim().startsWith('{')) {
          isStreamingText = false;
          jsonCandidate = chunk; // 开始新的JSON候选
        }
        
        if (isStreamingText) {
          // 流式传输文本内容，实时显示
          try {
            // 尝试解析为JSON，如果成功则说明是结构化响应
            const testJson = JSON.parse(fullBuffer.trim());
            if (testJson.success !== undefined) {
              isStreamingText = false;
              structuredResponse = testJson;
              break;
            }
          } catch (e) {
            // 不是JSON，继续作为文本流处理
            if (fullBuffer !== lastAnalysis) {
              lastAnalysis = fullBuffer;
              gotAnalysis = true;
              if (!analysisMsgId) {
                setMessages(prev => prev.filter(msg => msg.id !== thinkingMsgId));
                const analysisMsg = addMessage(fullBuffer, MESSAGE_TYPES.ANALYSIS);
                analysisMsgId = analysisMsg.id;
              } else {
                updateMessage(analysisMsgId, { content: fullBuffer });
              }
            }
          }
        } else {
          // 收集JSON响应
          jsonCandidate += chunk;
          try {
            const result = JSON.parse(jsonCandidate);
            if (result.success !== undefined) {
              structuredResponse = result;
              break;
            }
          } catch (e) {
            // JSON不完整，继续收集
          }
        }
      }
      if (gotAnalysis && !gotCards) {
        const newThinkingMsg = addMessage('', MESSAGE_TYPES.THINKING);
        thinkingMsgId = newThinkingMsg.id;
      }
      if (!fullBuffer.trim()) {
        // 清除思考消息
        setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
        addMessage('❌ 没有收到响应内容', MESSAGE_TYPES.ERROR);
      } else {
        try {
          // 优先使用结构化响应，如果没有则尝试解析整个缓冲区
          const result = structuredResponse || JSON.parse(fullBuffer);
          
          if (result.success === false) {
            // 清除思考消息
            setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
            addMessage(`❌ ${result.error}`, MESSAGE_TYPES.ERROR);
          } else {
            // 移除所有思考动画
            setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
            // 只在流式结束后统一处理 answer/card/action
            try {
              await handleAuditAgentAction(result, content); // 使用单据审核专用的处理函数
            } catch (error) {
              console.error('handleAuditAgentAction failed:', error);
              // 清除思考消息
              setMessages(prev => prev.filter(msg =>
                msg.type !== MESSAGE_TYPES.THINKING &&
                msg.type !== MESSAGE_TYPES.ANALYSIS &&
                msg.type !== MESSAGE_TYPES.CREATING_CARDS
              ));
              addMessage(`处理AI响应时发生错误: ${error.message}`, MESSAGE_TYPES.ERROR);
            }
          }
        } catch (e) {
          // 清除思考消息
          setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
          if (analysisMsgId) {
            updateMessage(analysisMsgId, { content: fullBuffer });
          } else {
            // 如果没有分析消息，显示原始内容
            addMessage(fullBuffer, MESSAGE_TYPES.ASSISTANT);
          }
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        addMessage(`发送失败: ${error.message}`, MESSAGE_TYPES.ERROR);
        showToast('发送消息失败', 'error');
      } else {
        // 如果是用户主动停止，清除思考消息
        setMessages(prev => prev.filter(msg =>
          msg.type !== MESSAGE_TYPES.THINKING &&
          msg.type !== MESSAGE_TYPES.ANALYSIS &&
          msg.type !== MESSAGE_TYPES.CREATING_CARDS
        ));
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
      streamingMessageRef.current = null;
      setAutoLooping(false);
      autoLoopRef.current = false;
    }
  };

  // 单据审核模式的 action 处理主函数
  const handleAuditAgentAction = async (response, userInput) => {
    try {
      console.log('[DEBUG] handleAuditAgentAction called', response);
      const { action, answer, data, is_finished, needs_more_info, required_info, audit_conclusion } = response;
      
      // 尝试从answer中解析JSON并提取answer_content
      let userFriendlyMessage = '';
      let cleanAnswer = answer ? cleanJsonFromText(answer) : '';
      
      // 尝试解析JSON格式的answer以提取answer_content
      if (answer) {
        try {
          // 提取JSON部分
          const jsonMatch = answer.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const jsonData = JSON.parse(jsonMatch[0]);
            // 如果有answer_content字段，使用它作为用户友好消息
            if (jsonData.answer_content) {
              userFriendlyMessage = jsonData.answer_content;
            }
          }
        } catch (e) {
          // 如果解析失败，使用清理后的answer
          userFriendlyMessage = cleanAnswer;
        }
      }
      
      // 如果没有提取到answer_content，使用清理后的answer
      if (!userFriendlyMessage) {
        userFriendlyMessage = cleanAnswer;
      }
      
      switch (action) {
        case 'audit_complete':
          // 审核完成
          if (userFriendlyMessage) {
            addMessage(userFriendlyMessage, MESSAGE_TYPES.ASSISTANT);
          }
          showToast('单据审核完成');
          break;
        case 'request_more_info':
          // 需要更多信息 - 这是关键修复点
          if (needs_more_info) {
            let infoMessage = "";
            
            // 优先使用answer_content作为主要信息
            if (userFriendlyMessage) {
              infoMessage = userFriendlyMessage + "\n\n";
            } else {
              infoMessage = "为了完成单据审核，请提供以下信息：\n\n";
            }
            
            // 添加需要补充的具体信息列表
            if (required_info && Array.isArray(required_info) && required_info.length > 0) {
              infoMessage += "📋 需要补充的信息：\n";
              required_info.forEach((info, index) => {
                infoMessage += `${index + 1}. ${info}\n`;
              });
            } else {
              infoMessage += "📋 需要补充的信息：\n";
              infoMessage += "1. 请提供更完整的单据信息\n";
            }
            
            infoMessage += "\n💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。";
            
            addMessage(infoMessage, MESSAGE_TYPES.ASSISTANT);
            showToast('需要补充信息以完成审核', 'info');
          } else if (userFriendlyMessage) {
            // 即使没有明确标记needs_more_info，如果audit_conclusion表明需要更多信息，也显示提示
            if (audit_conclusion === '需要更多信息' || audit_conclusion?.includes('需要')) {
              let infoMessage = userFriendlyMessage + "\n\n";
              infoMessage += "💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。";
              
              addMessage(infoMessage, MESSAGE_TYPES.ASSISTANT);
              showToast('需要补充信息以完成审核', 'info');
            } else {
              addMessage(userFriendlyMessage, MESSAGE_TYPES.ASSISTANT);
            }
          }
          break;
        case 'audit_rejected':
          // 审核拒绝
          if (userFriendlyMessage) {
            addMessage(userFriendlyMessage, MESSAGE_TYPES.ASSISTANT);
          }
          showToast('单据审核未通过', 'error');
          break;
        case 'none':
          if (userFriendlyMessage) {
            addMessage(userFriendlyMessage, MESSAGE_TYPES.ASSISTANT);
          }
          showToast('操作已完成');
          break;
        default:
          // 默认情况下，如果有answer就显示，否则显示默认消息
          if (userFriendlyMessage) {
            addMessage(userFriendlyMessage, MESSAGE_TYPES.ASSISTANT);
          } else {
            showToast('暂不支持该操作');
          }
      }
      
      if (!is_finished) {
        // 如果未完成且需要更多信息，可以在这里添加自动提示或其他处理
        if (needs_more_info) {
          console.log('Audit requires more information from user');
        }
      }
    } catch (error) {
      console.error('handleAuditAgentAction error:', error);
      // 清除思考消息
      setMessages(prev => prev.filter(msg =>
        msg.type !== MESSAGE_TYPES.THINKING &&
        msg.type !== MESSAGE_TYPES.ANALYSIS &&
        msg.type !== MESSAGE_TYPES.CREATING_CARDS
      ));
      addMessage(`处理响应时发生错误: ${error.message}`, MESSAGE_TYPES.ERROR);
      showToast('处理响应失败', 'error');
    }
  };

  // 覆盖原 sendMessage，支持多轮自洽
  const sendMessage = async (content) => {
    try {
      // 检查功能选择
      if (selectedFunction === '单据审核') {
        // 单据审核功能
        if (!uploadedFiles || uploadedFiles.length === 0) {
          showToast('请上传单据文件进行审核', 'error');
          return;
        }
        
        // 1. 立即设置加载和流式状态
        setIsLoading(true);
        setIsStreaming(true);

        // 2. 保存当前文件列表并立即清空显示
        const currentFiles = [...uploadedFiles];
        setUploadedFiles([]);

        // 3. 立即插入文件消息，并记录消息ID用于后续更新
        const fileMsgIds = [];
        currentFiles.forEach(fileInfo => {
          const localUrl = URL.createObjectURL(fileInfo.file);
          const msg = addMessage('', MESSAGE_TYPES.FILE, {
            fileName: fileInfo.name,
            fileType: fileInfo.type,
            processing: true,
            fileUrl: localUrl,
          });
          fileMsgIds.push(msg.id);
        });

        // 4. 上传文件
        let fileInfos = [];
        for (let i = 0; i < currentFiles.length; i++) {
          const fileInfo = currentFiles[i];
          const formData = new FormData();
          formData.append('file', fileInfo.file);
          try {
            const response = await fetch(`${backendBase}/agent/v2/upload`, {
              method: 'POST',
              headers: {
                'X-API-Key': aiConfig.api_key,
                'X-Base-URL': aiConfig.base_url,
                'X-Model': aiConfig.model
              },
              body: formData
            });
            if (!response.ok) {
              throw new Error(`HTTP ${response.status}`);
            }
            const result = await response.json();
            if (result.success && result.file_info) {
              fileInfos.push({
                file_path: result.file_info.path, // 用本地路径给后端
                file_name: fileInfo.name,
                file_type: fileInfo.type
              });
              // 上传成功，更新消息 processing: false 和 fileUrl
              updateMessage(fileMsgIds[i], { processing: false, fileUrl: `${backendBase}${result.file_info.url}` });
            } else {
              updateMessage(fileMsgIds[i], { processing: false, error: true });
            }
          } catch (error) {
            console.error('文件上传失败:', error);
            showToast(`文件 ${fileInfo.name} 上传失败`, 'error');
            updateMessage(fileMsgIds[i], { processing: false, error: true });
          }
        }

        // 5. 发送单据审核请求
        setContext({});
        setAutoLooping(true);
        autoLoopRef.current = true;
        await sendAuditRequestWithContext(content, { files: fileInfos });
        setIsLoading(false);
        setIsStreaming(false);
        return;
      }

      if (selectedFunction === '财务咨询') {
        // 财务咨询功能 - 目前只是提示，后续开发
        addMessage('财务咨询功能正在开发中，敬请期待！', MESSAGE_TYPES.SYSTEM);
        showToast('财务咨询功能正在开发中');
        return;
      }

      // 记账凭证功能 - 现有流程
      // 1. 立即设置加载和流式状态
      setIsLoading(true);
      setIsStreaming(true);

      // 2. 保存当前文件列表并立即清空显示
      const currentFiles = [...uploadedFiles];
      setUploadedFiles([]);

      // 3. 立即插入文件消息，并记录消息ID用于后续更新
      const fileMsgIds = [];
      currentFiles.forEach(fileInfo => {
        const localUrl = URL.createObjectURL(fileInfo.file);
        const msg = addMessage('', MESSAGE_TYPES.FILE, {
          fileName: fileInfo.name,
          fileType: fileInfo.type,
          processing: true,
          fileUrl: localUrl,
        });
        fileMsgIds.push(msg.id);
      });

      // 4. 上传文件
      let fileInfos = [];
      for (let i = 0; i < currentFiles.length; i++) {
        const fileInfo = currentFiles[i];
        const formData = new FormData();
        formData.append('file', fileInfo.file);
        try {
          const response = await fetch(`${backendBase}/agent/v2/upload`, {
            method: 'POST',
            headers: {
              'X-API-Key': aiConfig.api_key,
              'X-Base-URL': aiConfig.base_url,
              'X-Model': aiConfig.model
            },
            body: formData
          });
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          const result = await response.json();
          if (result.success && result.file_info) {
            fileInfos.push({
              file_path: result.file_info.path, // 用本地路径给后端
              file_name: fileInfo.name,
              file_type: fileInfo.type
            });
            // 3.1 上传成功，更新消息 processing: false 和 fileUrl
            updateMessage(fileMsgIds[i], { processing: false, fileUrl: `${backendBase}${result.file_info.url}` });
          } else {
            updateMessage(fileMsgIds[i], { processing: false, error: true });
          }
        } catch (error) {
          console.error('文件上传失败:', error);
          showToast(`文件 ${fileInfo.name} 上传失败`, 'error');
          updateMessage(fileMsgIds[i], { processing: false, error: true });
        }
      }

      setContext({});
      setAutoLooping(true);
      autoLoopRef.current = true;
      await sendMessageWithContext(content, { files: fileInfos });
      setIsLoading(false);
      setIsStreaming(false);
    } catch (error) {
      console.error('DEBUG: Error in sendMessage:', error);
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  // 停止生成
  const stopGeneration = () => {
    if (abortController) {
      abortController.abort();
      // 清除思考和分析消息
      setMessages(prev => prev.filter(msg =>
        msg.type !== MESSAGE_TYPES.THINKING &&
        msg.type !== MESSAGE_TYPES.ANALYSIS &&
        msg.type !== MESSAGE_TYPES.CREATING_CARDS
      ));
      // 添加用户反馈消息
      addMessage('生成已停止', MESSAGE_TYPES.SYSTEM);
      showToast('生成已停止');
      // 重置状态
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
    }
  };

  // 处理文件选择
  const handleFileSelect = (files) => {
    const fileArray = Array.from(files);
    console.log('handleFileSelect files:', fileArray);
    const newFiles = fileArray
      .filter(file => file && file.name)
      .map(file => ({
        id: Date.now() + Math.random(),
        file: file,
        name: file.name,
        size: file.size,
        type: file.type
          ? (file.type.startsWith('image/') ? 'image' : (file.type === 'application/pdf' ? 'pdf' : 'file'))
          : 'file'
      }));
    console.log('newFiles:', newFiles);
    setUploadedFiles(prev => {
      const result = [...prev, ...newFiles];
      console.log('setUploadedFiles result:', result);
      return result;
    });
  };

  // 删除文件
  const removeFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // 处理文件上传和发送
  const handleFileUpload = async () => {
    if (uploadedFiles.length === 0) {
      showToast('请先选择文件', 'error');
      return;
    }

    // 准备文件信息
    const fileInfos = [];
    for (const fileInfo of uploadedFiles) {
      // 上传文件到服务器
      const formData = new FormData();
      formData.append('file', fileInfo.file);

      try {
        const response = await fetch(`${backendBase}/agent/v2/upload`, {
          method: 'POST',
          headers: {
            'X-API-Key': aiConfig.api_key,
            'X-Base-URL': aiConfig.base_url,
            'X-Model': aiConfig.model
          },
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();
        if (result.success && result.file_info) {
          fileInfos.push({
            file_path: result.file_info.path,
            file_name: fileInfo.name,
            file_type: fileInfo.type
          });
        }
      } catch (error) {
        console.error('文件上传失败:', error);
        showToast(`文件 ${fileInfo.name} 上传失败`, 'error');
      }
    }

    // 发送消息和文件
    if (fileInfos.length > 0) {
      await sendMessageWithContext(input, { files: fileInfos });
      setInput('');
      setUploadedFiles([]);
    }
  };

  // 清空对话
  const clearConversation = async () => {
    if (sessionId) {
      try {
        await fetch(`${backendBase}/agent/v2/history/${sessionId}`, {
          method: 'DELETE'
        });
      } catch (error) {
        console.error('清空对话历史失败:', error);
      }
    }
    setMessages([]);
    showToast('对话已清空');
  };

  // 重试消息
  const retryMessage = (messageId) => {
    const message = messages.find(m => m.id === messageId);
    if (message && message.type === MESSAGE_TYPES.ASSISTANT) {
      // 找到对应的用户消息并重新发送
      const messageIndex = messages.findIndex(m => m.id === messageId);
      const userMessage = messages[messageIndex - 1];
      if (userMessage && userMessage.type === MESSAGE_TYPES.USER) {
        sendMessage(userMessage.content);
      }
    }
  };


  // 拖拽上传
  const handleDragOver = (e) => {
    e.preventDefault();
  };
  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    handleFileSelect(files);
  };

  // 粘贴图片上传
  useEffect(() => {
    const handlePaste = (e) => {
      if (e.clipboardData && e.clipboardData.files.length > 0) {
        const files = Array.from(e.clipboardData.files);
        handleFileSelect(files);
      }
    };
    window.addEventListener('paste', handlePaste);
    return () => window.removeEventListener('paste', handlePaste);
  }, []);

  return (
    <div className="flex flex-col h-full bg-white"
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* 头部 - 只在非侧边栏模式下显示 */}
      {!inSidebar && (
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <div className="flex items-center gap-2">
            <MessageSquare className="text-blue-600" size={20} />
            <h2 className="font-semibold">智能助手</h2>
            {sessionId && typeof sessionId === 'string' && (
              <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                {sessionId.slice(0, 8)}...
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {isStreaming && (
              <button
                onClick={stopGeneration}
                className="p-2 text-red-600 hover:bg-red-50 rounded"
                title="停止生成"
              >
                <StopCircle size={16} />
              </button>
            )}

          </div>
        </div>
      )}

      {/* 消息列表 */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-6 space-y-6"
      >
        {messages.length === 0 && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center opacity-60 select-none pointer-events-none">
              <div className="mb-8">
                <MessageSquare size={80} className="text-gray-400 mx-auto mb-6" strokeWidth={1.5} />
              </div>
              <h3 className="text-2xl font-medium text-gray-500 mb-4 tracking-wide">
                开始与智能助手对话
              </h3>
              <p className="text-gray-400 text-lg font-normal leading-relaxed max-w-md mx-auto">
                发送消息或上传文档<br />
                获得专业的会计服务支持
              </p>
            </div>
          </div>
        )}

        {messages.map((message, index) => (
          <div
            key={message.id}
            className="animate-fade-in-up"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <Message
              message={message}
              onRetry={retryMessage}
              handleCardConfirm={handleCardConfirm}
              sendMessageWithContext={sendMessageWithContext}
            />
          </div>
        ))}

        <div ref={messagesEndRef} />
      </div>



      {/* 现代化输入区域 */}
      <div className="p-6 border-t border-white/10 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm">
        {/* 功能提示 */}
        {selectedFunction === '单据审核' && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-blue-700">
              <CheckCircle size={16} />
              <span className="text-sm font-medium">单据审核模式</span>
              <span className="text-xs bg-blue-100 px-2 py-1 rounded-full">已启用</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">请上传单据文件进行智能审核</p>
          </div>
        )}

        {selectedFunction === '记账凭证' && (
          <div className="mb-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
            <div className="flex items-center gap-2 text-purple-700">
              <FileText size={16} />
              <span className="text-sm font-medium">记账凭证模式</span>
              <span className="text-xs bg-purple-100 px-2 py-1 rounded-full">已启用</span>
            </div>
            <p className="text-xs text-purple-600 mt-1">请上传凭证进行记账凭证生成</p>
          </div>
        )}

        {selectedFunction === '财务咨询' && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-700">
              <MessageCircle size={16} />
              <span className="text-sm font-medium">财务咨询模式</span>
              <span className="text-xs bg-green-100 px-2 py-1 rounded-full">开发中</span>
            </div>
            <p className="text-xs text-green-600 mt-1">提供专业的财务咨询和建议服务</p>
          </div>
        )}

        <SimpleChatInput
          value={input}
          onChange={setInput}
          onSend={() => sendMessage(input)}
          onFileSelect={handleFileSelect}
          uploadedFiles={uploadedFiles}
          onRemoveFile={(index) => setUploadedFiles(prev => prev.filter((_, i) => i !== index))}
          isLoading={isLoading}
          isStreaming={isStreaming}
          onStop={stopGeneration}
          placeholder={
            selectedFunction === '单据审核' ? "上传单据进行审核..." :
              selectedFunction === '财务咨询' ? "请描述您的财务问题..." :
                "输入消息... (Shift+Enter 换行)"
          }
          disabled={false}
          selectedFunction={selectedFunction}
          onFunctionChange={setSelectedFunction}
        />
      </div>

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default Agent;
