import React, { useState } from 'react';
import ModernChatInput from './ModernChatInput';
import { MessageSquare, Sparkles } from 'lucide-react';

const InputDemo = () => {
  const [input, setInput] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);

  const handleSend = () => {
    if (!input.trim() && uploadedFiles.length === 0) return;
    
    // 添加消息到列表
    const newMessage = {
      id: Date.now(),
      text: input,
      files: [...uploadedFiles],
      timestamp: new Date().toLocaleTimeString()
    };
    
    setMessages(prev => [...prev, newMessage]);
    setInput('');
    setUploadedFiles([]);
    
    // 模拟加载状态
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        text: '这是一个模拟的AI回复，展示现代化输入框的效果。',
        isAI: true,
        timestamp: new Date().toLocaleTimeString()
      }]);
    }, 2000);
  };

  const handleFileSelect = (files) => {
    const newFiles = Array.from(files).map(file => ({
      id: Date.now() + Math.random(),
      file: file,
      name: file.name,
      size: file.size,
      type: file.type
    }));
    setUploadedFiles(prev => [...prev, ...newFiles]);
  };

  const handleRemoveFile = (index) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleStop = () => {
    setIsStreaming(false);
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        {/* 标题 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            现代化聊天输入框演示
          </h1>
          <p className="text-gray-600 text-lg">
            体验类似Claude、ChatGPT的现代化输入体验
          </p>
        </div>

        {/* 聊天区域 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden">
          {/* 消息列表 */}
          <div className="h-96 overflow-y-auto p-6 space-y-4">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageSquare size={32} className="text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  开始对话
                </h3>
                <p className="text-gray-600">
                  试试发送消息或上传文件
                </p>
              </div>
            ) : (
              messages.map((message) => (
                <div key={message.id} className={`flex ${message.isAI ? 'justify-start' : 'justify-end'}`}>
                  <div className={`max-w-[80%] rounded-2xl p-4 ${
                    message.isAI 
                      ? 'bg-gray-100 text-gray-800' 
                      : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                  }`}>
                    {message.isAI && (
                      <div className="flex items-center gap-2 mb-2">
                        <Sparkles size={16} className="text-blue-500" />
                        <span className="text-sm font-medium text-gray-600">AI助手</span>
                      </div>
                    )}
                    <p className="leading-relaxed">{message.text}</p>
                    {message.files && message.files.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {message.files.map((file, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm opacity-90">
                            📎 {file.name} ({(file.size / 1024).toFixed(1)}KB)
                          </div>
                        ))}
                      </div>
                    )}
                    <div className="text-xs opacity-70 mt-2">
                      {message.timestamp}
                    </div>
                  </div>
                </div>
              ))
            )}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-2xl p-4 max-w-[80%]">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
                    <span className="text-gray-600">AI正在思考...</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 输入区域 */}
          <div className="border-t border-gray-200 p-6">
            <ModernChatInput
              value={input}
              onChange={setInput}
              onSend={handleSend}
              onFileSelect={handleFileSelect}
              uploadedFiles={uploadedFiles}
              onRemoveFile={handleRemoveFile}
              isLoading={isLoading}
              isStreaming={isStreaming}
              onStop={handleStop}
              placeholder="Ask a question or describe a task..."
              disabled={false}
            />
          </div>
        </div>

        {/* 功能说明 */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <h3 className="font-semibold text-gray-800 mb-3">🎯 智能输入</h3>
            <p className="text-gray-600 text-sm">
              自动调整高度，支持Shift+Enter换行，Enter发送
            </p>
          </div>
          
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <h3 className="font-semibold text-gray-800 mb-3">📎 文件上传</h3>
            <p className="text-gray-600 text-sm">
              支持拖拽上传，图片和文档分类，可重复选择相同文件
            </p>
          </div>
          
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <h3 className="font-semibold text-gray-800 mb-3">✨ 现代设计</h3>
            <p className="text-gray-600 text-sm">
              类似Claude的现代化界面，流畅的动画和交互
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InputDemo;