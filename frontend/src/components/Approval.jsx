import React from 'react';
import { CheckCircle, Clock, XCircle, FileText, User, Calendar } from 'lucide-react';

const Approval = () => {
  return (
    <div className="p-8 max-w-6xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">审批管理</h1>
        <p className="text-gray-600">管理和处理各类业务审批流程</p>
      </div>

      {/* 开发中提示 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-8 text-center shadow-soft">
        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
          <CheckCircle size={40} className="text-white" />
        </div>
        <h3 className="text-2xl font-semibold text-gray-800 mb-4">
          审批功能开发中
        </h3>
        <p className="text-gray-600 leading-relaxed max-w-2xl mx-auto mb-6">
          我们正在开发强大的审批管理功能，包括审批流程设计、审批任务处理、审批历史查询等功能，敬请期待！
        </p>
        
        {/* 功能预览 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Clock size={24} className="text-green-600" />
            </div>
            <h4 className="font-semibold text-gray-800 mb-2">待审批任务</h4>
            <p className="text-sm text-gray-600">查看和处理待审批的业务单据</p>
          </div>
          
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FileText size={24} className="text-blue-600" />
            </div>
            <h4 className="font-semibold text-gray-800 mb-2">审批流程</h4>
            <p className="text-sm text-gray-600">设计和管理各类审批流程</p>
          </div>
          
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <User size={24} className="text-purple-600" />
            </div>
            <h4 className="font-semibold text-gray-800 mb-2">审批历史</h4>
            <p className="text-sm text-gray-600">查询和统计审批处理记录</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Approval;