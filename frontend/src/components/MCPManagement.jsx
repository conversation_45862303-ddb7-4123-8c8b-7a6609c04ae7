import React, { useState, useEffect } from 'react';
import { 
  Play, 
  Square, 
  RefreshCw, 
  Settings, 
  Plus, 
  Trash2, 
  Edit3,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import toast from 'react-hot-toast';
import api from '../api';

const MCPManagement = () => {
  const [servers, setServers] = useState({});
  const [serverStatus, setServerStatus] = useState({});
  const [tools, setTools] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddServer, setShowAddServer] = useState(false);
  const [editingServer, setEditingServer] = useState(null);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(null);

  // 新服务器表单状态
  const [newServer, setNewServer] = useState({
    name: '',
    transport: 'stdio',
    command: '',
    args: [],
    url: '',
    env: {},
    disabled: false,
    auto_approve: [],
    timeout: 30,
    retryCount: 3
  });

  useEffect(() => {
    loadMCPData();
  }, []);

  const loadMCPData = async () => {
    try {
      setLoading(true);
      
      // 并行加载配置、状态和工具
      const [configRes, statusRes, toolsRes] = await Promise.all([
        api.get('/api/mcp/config'),
        api.get('/api/mcp/servers/status'),
        api.get('/api/mcp/tools')
      ]);

      if (configRes.success) {
        setServers(configRes.config);
      }

      if (statusRes.success) {
        setServerStatus(statusRes.servers);
      }

      if (toolsRes.success) {
        setTools(toolsRes.tools);
      }
    } catch (error) {
      console.error('加载 MCP 数据失败:', error);
      toast.error('加载 MCP 数据失败');
    } finally {
      setLoading(false);
    }
  };

  const startServer = async (serverName) => {
    try {
      const response = await api.post(`/api/mcp/servers/${serverName}/start`);
      if (response.success) {
        toast.success(`服务器 ${serverName} 启动成功`);
        await loadServerStatus();
      }
    } catch (error) {
      console.error('启动服务器失败:', error);
      toast.error(`启动服务器失败: ${error.message}`);
    }
  };

  const stopServer = async (serverName) => {
    try {
      const response = await api.post(`/api/mcp/servers/${serverName}/stop`);
      if (response.success) {
        toast.success(`服务器 ${serverName} 已停止`);
        await loadServerStatus();
      }
    } catch (error) {
      console.error('停止服务器失败:', error);
      toast.error(`停止服务器失败: ${error.message}`);
    }
  };

  const startAllServers = async () => {
    try {
      const response = await api.post('/api/mcp/servers/start-all');
      if (response.success) {
        toast.success('所有服务器启动完成');
        await loadServerStatus();
      }
    } catch (error) {
      console.error('启动所有服务器失败:', error);
      toast.error('启动所有服务器失败');
    }
  };

  const stopAllServers = async () => {
    try {
      const response = await api.post('/api/mcp/servers/stop-all');
      if (response.success) {
        toast.success('所有服务器已停止');
        await loadServerStatus();
      }
    } catch (error) {
      console.error('停止所有服务器失败:', error);
      toast.error('停止所有服务器失败');
    }
  };

  const loadServerStatus = async () => {
    try {
      const response = await api.get('/api/mcp/servers/status');
      if (response.success) {
        setServerStatus(response.servers);
      }
    } catch (error) {
      console.error('加载服务器状态失败:', error);
    }
  };

  const reloadConfig = async () => {
    try {
      const response = await api.post('/api/mcp/reload');
      if (response.success) {
        toast.success('配置已重新加载');
        await loadMCPData();
      }
    } catch (error) {
      console.error('重新加载配置失败:', error);
      toast.error('重新加载配置失败');
    }
  };

  const saveServerConfig = async () => {
    try {
      setSaving(true);
      const updatedServers = { ...servers };
      
      if (editingServer) {
        // 编辑现有服务器
        updatedServers[editingServer] = { ...newServer };
      } else {
        // 添加新服务器
        if (!newServer.name) {
          toast.error('请输入服务器名称');
          setSaving(false);
          return;
        }
        if (updatedServers[newServer.name]) {
          toast.error('服务器名称已存在');
          setSaving(false);
          return;
        }
        updatedServers[newServer.name] = { ...newServer };
      }

      const response = await api.post('/api/mcp/config', updatedServers);
      if (response.success) {
        toast.success('配置已保存');
        setShowAddServer(false);
        setEditingServer(null);
        resetNewServerForm();
        await loadMCPData();
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      toast.error('保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  const deleteServer = async (serverName) => {
    if (!confirm(`确定要删除服务器 ${serverName} 吗？`)) {
      return;
    }

    try {
      setDeleting(serverName);
      const updatedServers = { ...servers };
      delete updatedServers[serverName];

      const response = await api.post('/api/mcp/config', updatedServers);
      if (response.success) {
        toast.success(`服务器 ${serverName} 已删除`);
        await loadMCPData();
      }
    } catch (error) {
      console.error('删除服务器失败:', error);
      toast.error('删除服务器失败');
    } finally {
      setDeleting(null);
    }
  };

  const resetNewServerForm = () => {
    setNewServer({
      name: '',
      transport: 'stdio',
      command: '',
      args: [],
      url: '',
      env: {},
      disabled: false,
      auto_approve: [],
      timeout: 30,
      retryCount: 3
    });
  };

  const startEditServer = (serverName) => {
    setEditingServer(serverName);
    setNewServer({ ...servers[serverName] });
    setShowAddServer(true);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'disconnected':
        return <XCircle className="w-5 h-5 text-gray-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'disabled':
        return <XCircle className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'connected':
        return '已连接';
      case 'disconnected':
        return '未连接';
      case 'error':
        return '错误';
      case 'disabled':
        return '已禁用';
      default:
        return '未知';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="w-8 h-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">加载 MCP 配置中...</span>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">MCP 服务管理</h1>
        <div className="flex space-x-2">
          <button
            onClick={reloadConfig}
            className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            重新加载
          </button>
          <button
            onClick={startAllServers}
            className="flex items-center px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            <Play className="w-4 h-4 mr-2" />
            启动全部
          </button>
          <button
            onClick={stopAllServers}
            className="flex items-center px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            <Square className="w-4 h-4 mr-2" />
            停止全部
          </button>
          <button
            onClick={() => {
              setShowAddServer(true);
              setEditingServer(null);
              resetNewServerForm();
            }}
            className="flex items-center px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-4 h-4 mr-2" />
            添加服务器
          </button>
        </div>
      </div>

      {/* 服务器列表 */}
      <div className="grid gap-4 mb-8">
        {Object.entries(servers).map(([name, config]) => {
          const status = serverStatus[name] || { status: 'not_configured' };
          
          return (
            <div key={name} className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(status.status)}
                  <div>
                    <h3 className="font-semibold text-gray-900">{name}</h3>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                        {config.transport || 'stdio'}
                      </span>
                      {config.transport === 'stdio' || !config.transport ? (
                        <span>{config.command} {config.args?.join(' ')}</span>
                      ) : (
                        <span>{config.url}</span>
                      )}
                    </div>
                    <p className="text-xs text-gray-500">
                      状态: {getStatusText(status.status)}
                      {status.connection_info && ` (${status.connection_info})`}
                    </p>
                    {status.error_message && (
                      <p className="text-xs text-red-500 mt-1">
                        错误: {status.error_message}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {config.disabled && (
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">
                      已禁用
                    </span>
                  )}
                  
                  {status.status === 'connected' ? (
                    <button
                      onClick={() => stopServer(name)}
                      className="flex items-center px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                    >
                      <Square className="w-4 h-4 mr-1" />
                      停止
                    </button>
                  ) : (
                    <button
                      onClick={() => startServer(name)}
                      disabled={config.disabled}
                      className="flex items-center px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Play className="w-4 h-4 mr-1" />
                      启动
                    </button>
                  )}
                  
                  <button
                    onClick={() => startEditServer(name)}
                    className="flex items-center px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  >
                    <Edit3 className="w-4 h-4 mr-1" />
                    编辑
                  </button>
                  
                  <button
                    onClick={() => deleteServer(name)}
                    disabled={deleting}
                    className="flex items-center px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {deleting && deleting === name ? (
                      <Loader className="w-4 h-4 mr-1 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4 mr-1" />
                    )}
                    {deleting && deleting === name ? '删除中...' : '删除'}
                  </button>
                </div>
              </div>
              
              {/* 显示工具列表 */}
              {status.tools && status.tools.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <p className="text-sm font-medium text-gray-700 mb-2">可用工具:</p>
                  <div className="flex flex-wrap gap-2">
                    {status.tools.map((tool, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                      >
                        {tool.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
        
        {Object.keys(servers).length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Settings className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>暂无 MCP 服务器配置</p>
            <p className="text-sm">点击"添加服务器"开始配置</p>
          </div>
        )}
      </div>

      {/* 添加/编辑服务器对话框 */}
      {showAddServer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">
              {editingServer ? '编辑服务器' : '添加 MCP 服务器'}
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  服务器名称
                </label>
                <input
                  type="text"
                  value={newServer.name}
                  onChange={(e) => setNewServer({ ...newServer, name: e.target.value })}
                  disabled={!!editingServer}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                  placeholder="例如: aws-docs"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  传输方式
                </label>
                <select
                  value={newServer.transport}
                  onChange={(e) => setNewServer({ ...newServer, transport: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="stdio">stdio (标准输入输出)</option>
                  <option value="sse">SSE (Server-Sent Events)</option>
                  <option value="streamable-http">Streamable HTTP</option>
                </select>
              </div>
              
              {/* stdio 配置 */}
              {newServer.transport === 'stdio' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      启动命令
                    </label>
                    <input
                      type="text"
                      value={newServer.command}
                      onChange={(e) => setNewServer({ ...newServer, command: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="例如: uvx"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      命令参数 (每行一个)
                    </label>
                    <textarea
                      value={newServer.args.join('\n')}
                      onChange={(e) => setNewServer({ 
                        ...newServer, 
                        args: e.target.value.split('\n').filter(arg => arg.trim()) 
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows="3"
                      placeholder="例如: awslabs.aws-documentation-mcp-server@latest"
                    />
                  </div>
                </>
              )}
              
              {/* HTTP/SSE 配置 */}
              {(newServer.transport === 'sse' || newServer.transport === 'streamable-http') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    服务器 URL
                  </label>
                  <input
                    type="url"
                    value={newServer.url}
                    onChange={(e) => setNewServer({ ...newServer, url: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder={newServer.transport === 'sse' ? 
                      "例如: http://localhost:3000/sse" : 
                      "例如: http://localhost:3000/api"
                    }
                  />
                </div>
              )}
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  环境变量 (JSON 格式)
                </label>
                <textarea
                  value={JSON.stringify(newServer.env, null, 2)}
                  onChange={(e) => {
                    try {
                      const env = JSON.parse(e.target.value || '{}');
                      setNewServer({ ...newServer, env });
                    } catch (error) {
                      // 忽略 JSON 解析错误，用户可能还在输入
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                  rows="4"
                  placeholder='{"FASTMCP_LOG_LEVEL": "ERROR"}'
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    连接超时 (秒)
                  </label>
                  <input
                    type="number"
                    value={newServer.timeout}
                    onChange={(e) => setNewServer({ ...newServer, timeout: parseInt(e.target.value) || 30 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="1"
                    max="300"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    重试次数
                  </label>
                  <input
                    type="number"
                    value={newServer.retryCount}
                    onChange={(e) => setNewServer({ ...newServer, retryCount: parseInt(e.target.value) || 3 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                    max="10"
                  />
                </div>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="disabled"
                  checked={newServer.disabled}
                  onChange={(e) => setNewServer({ ...newServer, disabled: e.target.checked })}
                  className="mr-2"
                />
                <label htmlFor="disabled" className="text-sm text-gray-700">
                  禁用此服务器
                </label>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddServer(false);
                  setEditingServer(null);
                  resetNewServerForm();
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                onClick={saveServerConfig}
                disabled={saving}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {saving ? (
                  <>
                    <Loader className="w-4 h-4 mr-2 animate-spin" />
                    {editingServer ? '保存中...' : '添加中...'}
                  </>
                ) : (
                  editingServer ? '保存' : '添加'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 工具列表 */}
      {tools.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">可用工具</h2>
          <div className="grid gap-3">
            {tools.map((tool, index) => (
              <div key={index} className="border border-gray-100 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">{tool.name}</h3>
                    <p className="text-sm text-gray-600">{tool.description}</p>
                    <p className="text-xs text-gray-500">来自: {tool.server_name}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MCPManagement;