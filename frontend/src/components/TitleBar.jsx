import React from 'react';
import { Minus, Square, X, <PERSON>ting<PERSON>, Maximize2 } from 'lucide-react';

const TitleBar = ({ title = "会计助手" }) => {
  const isElectron = typeof window !== 'undefined' && window.electronAPI;
  const platform = isElectron ? window.electronAPI.platform : 'web';

  const handleMinimize = () => {
    if (isElectron) {
      window.electronAPI.minimize();
    }
  };

  const handleMaximize = () => {
    if (isElectron) {
      window.electronAPI.maximize();
    }
  };

  const handleClose = () => {
    if (isElectron) {
      window.electronAPI.close();
    }
  };

  return (
    <div className="flex items-center justify-between h-12 bg-white/80 backdrop-blur-md border-b border-gray-200/50 select-none drag-region">
      {/* 左侧：应用图标和标题 */}
      <div className="flex items-center px-4 space-x-3">
        <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs font-bold">会</span>
        </div>
        <span className="text-sm font-medium text-gray-700">{title}</span>
      </div>

      {/* 中间：模型选择器（类似 Cherry Studio） */}
      <div className="flex items-center space-x-2 no-drag">
        <div className="flex items-center space-x-2 px-3 py-1.5 bg-gray-100/60 rounded-lg border border-gray-200/50">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-xs text-gray-600">智能助手</span>
          <span className="text-xs text-gray-400">|</span>
          <span className="text-xs text-gray-500">ERNIE-4.5</span>
        </div>
      </div>

      {/* 右侧：窗口控制按钮 */}
      {isElectron && (
        <div className="flex items-center no-drag">
          {platform === 'darwin' ? (
            // macOS 风格按钮
            <div className="flex items-center space-x-2 px-4">
              <button
                onClick={handleClose}
                className="w-3 h-3 bg-red-500 rounded-full hover:bg-red-600 transition-colors"
              />
              <button
                onClick={handleMinimize}
                className="w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-600 transition-colors"
              />
              <button
                onClick={handleMaximize}
                className="w-3 h-3 bg-green-500 rounded-full hover:bg-green-600 transition-colors"
              />
            </div>
          ) : (
            // Windows/Linux 风格按钮
            <div className="flex">
              <button
                onClick={handleMinimize}
                className="w-12 h-12 flex items-center justify-center hover:bg-gray-200/50 transition-colors"
              >
                <Minus size={14} className="text-gray-600" />
              </button>
              <button
                onClick={handleMaximize}
                className="w-12 h-12 flex items-center justify-center hover:bg-gray-200/50 transition-colors"
              >
                <Maximize2 size={14} className="text-gray-600" />
              </button>
              <button
                onClick={handleClose}
                className="w-12 h-12 flex items-center justify-center hover:bg-red-500 hover:text-white transition-colors"
              >
                <X size={14} />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TitleBar;