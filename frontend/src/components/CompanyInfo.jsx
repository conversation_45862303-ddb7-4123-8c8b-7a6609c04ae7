import React, { useState, useEffect } from 'react';
import { Save, Plus, Edit, Trash2 } from 'lucide-react';

const CompanyInfo = () => {
  const [companies, setCompanies] = useState([]);
  const [currentCompany, setCurrentCompany] = useState({
    id: null,
    name: '',
    business_scope: '',
    industry: '',
    company_size: '',
    tax_id: '',
    accounting_standards: '',
    established_date: '',
    registered_capital: '',
    status: 'active'
  });
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch company data from backend
  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8000/api/companies');
      if (!response.ok) {
        throw new Error('获取公司信息失败');
      }
      const data = await response.json();
      setCompanies(data);
      if (data.length > 0) {
        setCurrentCompany(data[0]);
      }
    } catch (err) {
      setError('获取公司信息失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentCompany(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      if (currentCompany.id) {
        // Update existing company
        const response = await fetch(`http://localhost:8000/api/companies/${currentCompany.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(currentCompany),
        });
        
        if (!response.ok) {
          throw new Error('更新公司信息失败');
        }
        
        const updatedCompany = await response.json();
        setCompanies(prev => prev.map(company =>
          company.id === updatedCompany.id ? updatedCompany : company
        ));
        setCurrentCompany(updatedCompany);
      } else {
        // Create new company
        const response = await fetch('http://localhost:8000/api/companies', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(currentCompany),
        });
        
        if (!response.ok) {
          throw new Error('创建公司信息失败');
        }
        
        const newCompany = await response.json();
        setCompanies(prev => [...prev, newCompany]);
        setCurrentCompany(newCompany);
      }
      setIsEditing(false);
    } catch (err) {
      setError('保存公司信息失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (company) => {
    setCurrentCompany(company);
    setIsEditing(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('确定要删除这家公司吗？')) {
      try {
        setLoading(true);
        const response = await fetch(`http://localhost:8000/api/companies/${id}`, {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          throw new Error('删除公司信息失败');
        }
        
        setCompanies(prev => prev.filter(company => company.id !== id));
        if (currentCompany.id === id) {
          setCurrentCompany({
            id: null,
            name: '',
            business_scope: '',
            industry: '',
            company_size: '',
            tax_id: '',
            accounting_standards: '',
            established_date: '',
            registered_capital: '',
            status: 'active'
          });
        }
      } catch (err) {
        setError('删除公司信息失败: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleNewCompany = () => {
    setCurrentCompany({
      id: null,
      name: '',
      business_scope: '',
      industry: '',
      company_size: '',
      tax_id: '',
      accounting_standards: '',
      established_date: '',
      registered_capital: '',
      status: 'active'
    });
    setIsEditing(true);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">公司信息管理</h2>
          <button
            onClick={handleNewCompany}
            className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Plus size={18} />
            新增公司
          </button>
        </div>
        
        {/* Company selection dropdown */}
        {companies.length > 1 && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择当前公司:
            </label>
            <select
              value={currentCompany.id || ''}
              onChange={(e) => {
                const companyId = parseInt(e.target.value);
                const company = companies.find(c => c.id === companyId);
                if (company) {
                  setCurrentCompany(company);
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {companies.map(company => (
                <option key={company.id} value={company.id}>
                  {company.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {companies.length > 0 && !isEditing && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-700 mb-4">公司列表</h3>
            <div className="space-y-4">
              {companies.map(company => (
                <div key={company.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-semibold text-gray-800">{company.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">行业: {company.industry}</p>
                      <p className="text-sm text-gray-600">税号: {company.tax_id}</p>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(company)}
                        className="p-2 text-gray-600 hover:bg-gray-100 rounded"
                        title="编辑"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(company.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded"
                        title="删除"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {isEditing && (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  公司名称 *
                </label>
                <input
                  type="text"
                  name="name"
                  value={currentCompany.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  行业
                </label>
                <input
                  type="text"
                  name="industry"
                  value={currentCompany.industry}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  税号
                </label>
                <input
                  type="text"
                  name="tax_id"
                  value={currentCompany.tax_id}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  公司规模
                </label>
                <select
                  name="company_size"
                  value={currentCompany.company_size}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择</option>
                  <option value="小型企业">小型企业</option>
                  <option value="中小型企业">中小型企业</option>
                  <option value="中型企业">中型企业</option>
                  <option value="大型企业">大型企业</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  经营范围
                </label>
                <textarea
                  name="business_scope"
                  value={currentCompany.business_scope}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  会计准则
                </label>
                <input
                  type="text"
                  name="accounting_standards"
                  value={currentCompany.accounting_standards}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  成立日期
                </label>
                <input
                  type="date"
                  name="established_date"
                  value={currentCompany.established_date}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  注册资本
                </label>
                <input
                  type="number"
                  name="registered_capital"
                  value={currentCompany.registered_capital}
                  onChange={handleInputChange}
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  状态
                </label>
                <select
                  name="status"
                  value={currentCompany.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50"
              >
                <Save size={18} />
                {loading ? '保存中...' : '保存'}
              </button>
            </div>
          </form>
        )}

        {!isEditing && companies.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">暂无公司信息</p>
            <button
              onClick={handleNewCompany}
              className="flex items-center gap-2 mx-auto bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus size={18} />
              添加第一家公司
            </button>
          </div>
        )}

        {!isEditing && companies.length > 0 && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">当前选中公司</h3>
            <p className="text-blue-700">{currentCompany.name}</p>
            <p className="text-sm text-blue-600 mt-1">{currentCompany.business_scope}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompanyInfo;