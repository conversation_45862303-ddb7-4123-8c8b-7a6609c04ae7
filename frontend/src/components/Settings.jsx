import React, { useState } from 'react';
import { Save, TestTube, Settings as SettingsIcon, Sparkles, Database, Server } from 'lucide-react';
import RAGManagement from './RAGManagement';
import MCPManagement from './MCPManagement';

const SettingsPanel = ({ aiConfig, setAiConfig, backendBase }) => {
  const [config, setConfig] = useState(aiConfig || {
    api_key: '',
    base_url: 'https://aistudio.baidu.com/llm/lmapi/v3',
    model: 'ernie-4.5-turbo-vl-preview'
  });
  
  const [testResult, setTestResult] = useState(null);
  const [isTesting, setIsTesting] = useState(false);
  const [activeTab, setActiveTab] = useState('settings');

  const handleSave = () => {
    setAiConfig(config);
    localStorage.setItem('aiConfig', JSON.stringify(config));
  };

  const handleTest = async () => {
    setIsTesting(true);
    setTestResult(null);
    try {
      const response = await fetch(`${backendBase}/ai/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '连接测试失败');
      }
      
      setTestResult({ success: true, message: '连接测试成功！' });
    } catch (error) {
      setTestResult({ 
        success: false, 
        message: `连接测试失败: ${error.message || '未知错误'}` 
      });
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto animate-fade-in-up">
      <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-200">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white shadow-lg">
              <SettingsIcon size={24} />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                AI设置中心
              </h2>
              <p className="text-sm text-gray-600 mt-1">配置您的AI服务和RAG管理</p>
            </div>
          </div>
        </div>

        {/* Tab 导航 */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => setActiveTab('settings')}
            className={`px-6 py-3 font-medium text-sm transition-all duration-200 border-b-2 ${
              activeTab === 'settings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <SettingsIcon size={16} />
              <span>AI服务器设置</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('rag')}
            className={`px-6 py-3 font-medium text-sm transition-all duration-200 border-b-2 ${
              activeTab === 'rag'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <Database size={16} />
              <span>RAG管理</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('mcp')}
            className={`px-6 py-3 font-medium text-sm transition-all duration-200 border-b-2 ${
              activeTab === 'mcp'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <Server size={16} />
              <span>MCP服务</span>
            </div>
          </button>
        </div>

        {/* AI服务器设置内容 */}
        {activeTab === 'settings' && (
          <div className="space-y-6 animate-fade-in">
            {/* API Key */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                API Key
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="password"
                value={config.api_key}
                onChange={(e) => setConfig({...config, api_key: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入您的API Key"
              />
            </div>

            {/* 基础URL */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                基础URL
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={config.base_url}
                onChange={(e) => setConfig({...config, base_url: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入API基础URL"
              />
            </div>

            {/* 模型名称 */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                模型名称
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={config.model}
                onChange={(e) => setConfig({...config, model: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入模型名称"
              />
            </div>

            {/* 测试结果 */}
            {testResult && (
              <div className={`p-4 rounded-xl border animate-slide-down ${
                testResult.success
                  ? 'bg-green-50 border-green-200 text-green-800'
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                <div className="flex items-center space-x-2">
                  <Sparkles size={16} className={testResult.success ? 'text-green-600' : 'text-red-600'} />
                  <div className="font-semibold">{testResult.success ? '连接成功' : '连接失败'}</div>
                </div>
                <div className="text-sm mt-1 opacity-90">{testResult.message}</div>
              </div>
            )}

            {/* 按钮组 */}
            <div className="flex space-x-3 pt-4">
              <button
                onClick={handleTest}
                disabled={isTesting}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {isTesting ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>测试中</span>
                  </div>
                ) : (
                  <>
                    <TestTube size={18} className="mr-2" />
                    测试连接
                  </>
                )}
              </button>
              
              <button
                onClick={handleSave}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
              >
                <Save size={18} className="mr-2" />
                保存设置
              </button>
            </div>
          </div>
        )}

        {/* RAG管理内容 */}
        {activeTab === 'rag' && (
          <div className="animate-fade-in">
            <RAGManagement
              backendBase={backendBase}
              aiConfig={config}
            />
          </div>
        )}

        {/* MCP服务管理内容 */}
        {activeTab === 'mcp' && (
          <div className="animate-fade-in">
            <MCPManagement />
          </div>
        )}
      </div>
    </div>
  );
};

export default SettingsPanel;