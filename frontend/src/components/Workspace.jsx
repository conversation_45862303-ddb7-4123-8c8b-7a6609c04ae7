import React from 'react';
import Voucher from './Voucher';
import CollapsibleCard from './CollapsibleCard';
import { FileText, Coins, Users, Receipt, Sparkles } from 'lucide-react';

const Workspace = ({ vouchers, subjects, assets, staffs }) => {
  const sections = [
    {
      key: 'subjects',
      data: subjects,
      title: '生成的科目',
      icon: <FileText className="w-5 h-5" />,
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'from-blue-50 to-cyan-50',
      render: (subject, index) => (
        <CollapsibleCard
          key={index}
          title={`科目：${subject.科目编码 || ''} ${subject.科目名称 || ''}`.trim()}
          date={subject.创建日期 || ''}
        >
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div><span className="font-medium text-gray-700">科目编码:</span> <span className="text-gray-900">{subject.科目编码}</span></div>
            <div><span className="font-medium text-gray-700">科目名称:</span> <span className="text-gray-900">{subject.科目名称}</span></div>
            <div><span className="font-medium text-gray-700">类别:</span> <span className="text-gray-900">{subject.类别}</span></div>
            <div><span className="font-medium text-gray-700">方向:</span> <span className="text-gray-900">{subject.方向}</span></div>
            {subject.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{subject.备注}</span></div>}
          </div>
        </CollapsibleCard>
      )
    },
    {
      key: 'assets',
      data: assets,
      title: '生成的资产',
      icon: <Coins className="w-5 h-5" />,
      color: 'from-emerald-500 to-teal-500',
      bgColor: 'from-emerald-50 to-teal-50',
      render: (asset, index) => (
        <CollapsibleCard
          key={index}
          title={`资产：${asset.资产编码 || ''} ${asset.资产名称 || ''}`.trim()}
          date={asset.购置日期 || ''}
        >
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div><span className="font-medium text-gray-700">资产编码:</span> <span className="text-gray-900">{asset.资产编码}</span></div>
            <div><span className="font-medium text-gray-700">资产名称:</span> <span className="text-gray-900">{asset.资产名称}</span></div>
            <div><span className="font-medium text-gray-700">类别:</span> <span className="text-gray-900">{asset.类别}</span></div>
            <div><span className="font-medium text-gray-700">原值:</span> <span className="text-gray-900">{asset.原值}</span></div>
            <div><span className="font-medium text-gray-700">净值:</span> <span className="text-gray-900">{asset.净值}</span></div>
            <div><span className="font-medium text-gray-700">购置日期:</span> <span className="text-gray-900">{asset.购置日期}</span></div>
            <div><span className="font-medium text-gray-700">使用年限:</span> <span className="text-gray-900">{asset.使用年限}</span></div>
            <div><span className="font-medium text-gray-700">状态:</span> <span className="text-gray-900">{asset.状态}</span></div>
            {asset.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{asset.备注}</span></div>}
          </div>
        </CollapsibleCard>
      )
    },
    {
      key: 'staffs',
      data: staffs,
      title: '生成的员工',
      icon: <Users className="w-5 h-5" />,
      color: 'from-purple-500 to-pink-500',
      bgColor: 'from-purple-50 to-pink-50',
      render: (staff, index) => (
        <CollapsibleCard
          key={index}
          title={`员工：${staff.工号 || ''} ${staff.姓名 || ''}`.trim()}
          date={staff.入职日期 || ''}
        >
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div><span className="font-medium text-gray-700">工号:</span> <span className="text-gray-900">{staff.工号}</span></div>
            <div><span className="font-medium text-gray-700">姓名:</span> <span className="text-gray-900">{staff.姓名}</span></div>
            <div><span className="font-medium text-gray-700">岗位编码:</span> <span className="text-gray-900">{staff.岗位编码}</span></div>
            <div><span className="font-medium text-gray-700">电话:</span> <span className="text-gray-900">{staff.电话}</span></div>
            <div><span className="font-medium text-gray-700">状态:</span> <span className="text-gray-900">{staff.状态}</span></div>
            {staff.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{staff.备注}</span></div>}
          </div>
        </CollapsibleCard>
      )
    },
    {
      key: 'vouchers',
      data: vouchers,
      title: '生成的凭证',
      icon: <Receipt className="w-5 h-5" />,
      color: 'from-orange-500 to-red-500',
      bgColor: 'from-orange-50 to-red-50',
      render: (voucher, index) => {
        // 处理后端返回的数据结构
        const debitEntries = voucher.借方 || voucher.debit || voucher.debit_entries || [];
        const creditEntries = voucher.贷方 || voucher.credit || voucher.credit_entries || [];

        return (
          <Voucher
            key={index}
            voucher={{
              ...voucher,
              date: voucher.日期 || voucher.date,
              summary: voucher.摘要 || voucher.summary,
              entries: [
                ...(Array.isArray(debitEntries) ? debitEntries : []).map(item => ({
                  ...item,
                  account: `${item.科目编码 || item.subject_code || ''} ${item.科目名称 || item.subject_name || ''}`.trim(),
                  amount: parseFloat(item.金额 || item.amount || 0),
                  type: 'debit'
                })),
                ...(Array.isArray(creditEntries) ? creditEntries : []).map(item => ({
                  ...item,
                  account: `${item.科目编码 || item.subject_code || ''} ${item.科目名称 || item.subject_name || ''}`.trim(),
                  amount: parseFloat(item.金额 || item.amount || 0),
                  type: 'credit'
                }))
              ]
            }}
            index={index}
            editable={false}
          />
        );
      }
    }
  ];

  const hasData = sections.some(section => section.data && section.data.length > 0);

  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden relative">
      <div className="flex-1 overflow-auto p-8">
        <div className="max-w-6xl mx-auto">
          {sections.map((section, sectionIndex) => (
            section.data && section.data.length > 0 && (
              <div key={section.key} className="mb-8 animate-fade-in-up" style={{ animationDelay: `${sectionIndex * 100}ms` }}>
                <div className={`bg-gradient-to-r ${section.bgColor} rounded-2xl p-6 mb-6 shadow-soft`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`p-3 bg-gradient-to-r ${section.color} rounded-xl text-white shadow-lg`}>
                        {section.icon}
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                          {section.title}
                        </h2>
                        <p className="text-sm text-gray-600 mt-1 flex items-center">
                          <Sparkles className="w-4 h-4 mr-1 text-yellow-500" />
                          共 {section.data.length} 个项目
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid gap-6">
                  {section.data.map((item, index) => (
                    <div key={index} className="animate-scale-in" style={{ animationDelay: `${(sectionIndex * 100) + (index * 50)}ms` }}>
                      {section.render(item, index)}
                    </div>
                  ))}
                </div>
              </div>
            )
          ))}

          {/* 空状态 */}
          {!hasData && (
            <div className="flex-1 flex items-center justify-center min-h-[60vh]">
              <div className="text-center opacity-60 select-none pointer-events-none">
                <div className="mb-8">
                  <Sparkles size={80} className="text-gray-400 mx-auto mb-6" strokeWidth={1.5} />
                </div>
                <h3 className="text-2xl font-medium text-gray-500 mb-4 tracking-wide">
                  工作区空空如也
                </h3>
                <p className="text-gray-400 text-lg font-normal leading-relaxed max-w-lg mx-auto">
                  与智能助手对话，流程审批、凭证生成等。<br />
                  它们将在这里显示
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Workspace;
