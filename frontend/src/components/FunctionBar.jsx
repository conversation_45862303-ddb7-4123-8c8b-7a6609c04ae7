import React from 'react'
import {
  <PERSON><PERSON><PERSON>,
  FileText,
  LibraryBig,
  BarChart2,
  CheckSquare,
  Coins,
  Receipt,
  PiggyBank,
  Users,
  DollarSign,
  Settings,
  Table,
  Building,
  CheckCircle
} from 'lucide-react'

const FunctionBar = ({ activeFeature, onFeatureSelect, features }) => {
  const icons = {
    agent: <BookOpen size={22} />,
    voucher: <FileText size={22} />,
    bookkeeping: <LibraryBig size={22} />,
    report: <BarChart2 size={22} />,
    settlement: <CheckSquare size={22} />,
    asset: <Coins size={22} />,
    invoice: <Receipt size={22} />,
    cashier: <PiggyBank size={22} />,
    salary: <Users size={22} />,
    tax: <DollarSign size={22} />,
    subject: <Table size={22} />,
    roleStaff: <Users size={22} />,
    company: <Building size={22} />,
    approval: <CheckCircle size={22} />,
    settings: <Settings size={22} />,
  }

  return (
    <div className="w-20 glass border-r border-white/20 flex flex-col h-full shadow-glass relative z-10">
      {/* 顶部装饰 */}
      <div className="h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
      
      <div className="flex-1 py-4">
        {Object.entries(features).map(([key, feature], index) => (
          <button
            key={key}
            onClick={() => onFeatureSelect(key)}
            className={`w-full p-3 mx-2 mb-2 flex flex-col items-center justify-center rounded-xl transition-all duration-300 group relative overflow-hidden
              ${activeFeature === key 
                ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg transform scale-105' 
                : 'text-gray-600 hover:bg-white/20 hover:text-gray-800 hover:scale-105'
              }`}
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {/* 活跃状态的光晕效果 */}
            {activeFeature === key && (
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-xl blur-xl"></div>
            )}
            
            <div className="relative z-10 flex flex-col items-center">
              <div className={`transition-transform duration-200 ${activeFeature === key ? 'scale-110' : 'group-hover:scale-110'}`}>
                {icons[key]}
              </div>
              <span className={`text-xs mt-1.5 font-medium transition-all duration-200 ${
                activeFeature === key ? 'text-white' : 'text-gray-600 group-hover:text-gray-800'
              }`}>
                {feature.name}
              </span>
            </div>
            
            {/* 悬停时的背景效果 */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        ))}
      </div>
    </div>
  )
}

export default FunctionBar 