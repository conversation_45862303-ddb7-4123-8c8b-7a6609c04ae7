import React, { useEffect, useState, useRef } from 'react';

const AUX_OPTIONS = ['部门', '项目', '客户', '供应商'];

const getCode = node => node['科目编码'] || node.code;
const getName = node => node['科目名称'] || node.name;
const getCategory = node => node['类别'] || node.category;
const getDirection = node => node['方向'] || node.direction;
const getLevel = node => node['级次'] || node.level;
const getParentCode = node => node['父级编码'] || node.parent_code;
const getAux = node => node['辅助核算'] && node['辅助核算'].length ? node['辅助核算'].join(',') : (node.aux || '-');
const getIsLeaf = node => (node['末级'] ?? node.is_leaf) ? '是' : '否';
const getStatus = node => node['状态'] || node.status;
const getRemark = node => node['备注'] || node.remark;
const getQuantity = node => (node['数量核算'] ?? node.quantity) ? '是' : '否';

const renderRows = (nodes, level = 0, expanded, toggleExpand, openEdit, handleDelete) => {
  return nodes.flatMap(node => {
    const code = getCode(node);
    if (!code) {
      // 跳过无效节点
      return [];
    }
    const rowKey = `${code}-${level}`;
    const rows = [
      <tr key={rowKey}>
        <td className="px-4 py-2 border">
          <span style={{ marginLeft: level * 20 }}>
            {node.children && node.children.length > 0 && (
              <button className="mr-1 text-blue-500" onClick={() => toggleExpand(code)}>
                {expanded[code] ? '▼' : '▶'}
              </button>
            )}
            {code}
          </span>
        </td>
        <td className="px-4 py-2 border">{getName(node)}</td>
        <td className="px-4 py-2 border">{getCategory(node)}</td>
        <td className="px-4 py-2 border">{getDirection(node)}</td>
        <td className="px-4 py-2 border">{getLevel(node)}</td>
        <td className="px-4 py-2 border">{getParentCode(node) || '-'}</td>
        <td className="px-4 py-2 border">{getAux(node)}</td>
        <td className="px-4 py-2 border">{getIsLeaf(node)}</td>
        <td className="px-4 py-2 border">{getStatus(node)}</td>
        <td className="px-4 py-2 border">{getRemark(node)}</td>
        <td className="px-4 py-2 border">{getQuantity(node)}</td>
        <td className="px-4 py-2 border">
          <button className="text-blue-500 hover:underline" onClick={() => openEdit(node)}>编辑</button>
          <button className="text-red-500 hover:underline ml-2" onClick={() => handleDelete(code)}>删除</button>
        </td>
      </tr>
    ];
    if (node.children && node.children.length > 0 && expanded[code]) {
      rows.push(...renderRows(node.children, level + 1, expanded, toggleExpand, openEdit, handleDelete));
    }
    return rows;
  });
};

const SubjectTable = () => {
  const [tree, setTree] = useState([]);
  const [expanded, setExpanded] = useState({});
  const fileInputRef = useRef();
  const [editSubject, setEditSubject] = useState(null);
  const [form, setForm] = useState({
    科目编码: '',
    科目名称: '',
    类别: '',
    方向: '',
    级次: 1,
    父级编码: '',
    辅助核算: [],
    末级: true,
    状态: '启用',
    备注: '',
    数量核算: false
  });
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [loadingTemplate, setLoadingTemplate] = useState(false);

  const fetchTree = async () => {
    const res = await fetch('http://localhost:8000/subjects/tree');
    const data = await res.json();
    setTree(data.tree || []);
  };

  const fetchTemplates = async () => {
    const res = await fetch('http://localhost:8000/subjects/templates');
    const data = await res.json();
    setTemplates(data);
  };

  useEffect(() => {
    fetchTree();
    fetchTemplates();
  }, []);

  const handleTemplateChange = async (e) => {
    const key = e.target.value;
    if (!key) return;
    if (!window.confirm('切换模板将覆盖当前科目表，是否继续？')) return;
    setLoadingTemplate(true);
    await fetch('http://localhost:8000/subjects/use-template', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ key }),
    });
    setSelectedTemplate(key);
    setLoadingTemplate(false);
    fetchTree();
  };

  const toggleExpand = code => {
    setExpanded(exp => ({ ...exp, [code]: !exp[code] }));
  };

  const handleExport = () => {
    window.open('http://localhost:8000/subjects/export', '_blank');
  };

  const handleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await fetch('http://localhost:8000/subjects/import', {
      method: 'POST',
      body: formData,
    });
    fetchTree();
    fileInputRef.current.value = '';
  };

  const handleDelete = async (code) => {
    if (!window.confirm('确定要删除该科目吗？')) return;
    const res = await fetch(`http://localhost:8000/subjects/${code}`, { method: 'DELETE' });
    if (res.status !== 200) {
      const data = await res.json();
      alert(data.detail || '删除失败');
    }
    fetchTree();
  };

  const openEdit = (subject) => {
    setEditSubject(subject);
    setForm({ ...subject, 辅助核算: subject['辅助核算'] || [] });
  };

  const closeEdit = () => {
    setEditSubject(null);
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    await fetch(`http://localhost:8000/subjects/${form['科目编码']}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(form),
    });
    closeEdit();
    fetchTree();
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-semibold">科目表（树状结构）</h2>
        <div className="space-x-2 flex items-center">
          <select
            className="border rounded px-2 py-1"
            value={selectedTemplate}
            onChange={handleTemplateChange}
            disabled={loadingTemplate}
          >
            <option value="">选择科目表模板</option>
            {templates.map(t => (
              <option key={t.key} value={t.key}>{t.name}</option>
            ))}
          </select>
          <input
            type="file"
            accept=".csv"
            ref={fileInputRef}
            style={{ display: 'none' }}
            onChange={handleImport}
          />
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => fileInputRef.current.click()}
          >
            导入
          </button>
          <button className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600" onClick={handleExport}>导出</button>
        </div>
      </div>
      <table className="min-w-full bg-white rounded shadow">
        <thead>
          <tr>
            <th className="px-4 py-2 border">科目编码</th>
            <th className="px-4 py-2 border">科目名称</th>
            <th className="px-4 py-2 border">类别</th>
            <th className="px-4 py-2 border">方向</th>
            <th className="px-4 py-2 border">级次</th>
            <th className="px-4 py-2 border">父级编码</th>
            <th className="px-4 py-2 border">辅助核算</th>
            <th className="px-4 py-2 border">末级</th>
            <th className="px-4 py-2 border">状态</th>
            <th className="px-4 py-2 border">备注</th>
            <th className="px-4 py-2 border">数量核算</th>
            <th className="px-4 py-2 border">操作</th>
          </tr>
        </thead>
        <tbody>
          {renderRows(tree, 0, expanded, toggleExpand, openEdit, handleDelete)}
        </tbody>
      </table>

      {/* 编辑弹窗 */}
      {editSubject && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <form className="bg-white p-6 rounded shadow w-96" onSubmit={handleEditSubmit}>
            <h3 className="text-lg font-semibold mb-4">编辑科目</h3>
            <div className="mb-3">
              <label className="block mb-1">科目编码</label>
              <input className="w-full border rounded px-2 py-1" value={form['科目编码']} disabled />
            </div>
            <div className="mb-3">
              <label className="block mb-1">科目名称</label>
              <input className="w-full border rounded px-2 py-1" value={form['科目名称']} onChange={e => setForm(f => ({ ...f, 科目名称: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">类别</label>
              <input className="w-full border rounded px-2 py-1" value={form['类别']} onChange={e => setForm(f => ({ ...f, 类别: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">方向</label>
              <input className="w-full border rounded px-2 py-1" value={form['方向']} onChange={e => setForm(f => ({ ...f, 方向: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">辅助核算</label>
              <select multiple className="w-full border rounded px-2 py-1" value={form['辅助核算']} onChange={e => {
                const opts = Array.from(e.target.selectedOptions).map(o => o.value);
                setForm(f => ({ ...f, 辅助核算: opts }));
              }}>
                {AUX_OPTIONS.map(opt => <option key={opt} value={opt}>{opt}</option>)}
              </select>
            </div>
            <div className="mb-3">
              <label className="block mb-1">状态</label>
              <select className="w-full border rounded px-2 py-1" value={form['状态']} onChange={e => setForm(f => ({ ...f, 状态: e.target.value }))}>
                <option value="启用">启用</option>
                <option value="禁用">禁用</option>
              </select>
            </div>
            <div className="mb-3">
              <label className="block mb-1">备注</label>
              <input className="w-full border rounded px-2 py-1" value={form['备注']} onChange={e => setForm(f => ({ ...f, 备注: e.target.value }))} />
            </div>
            <div className="mb-3 flex items-center">
              <input type="checkbox" id="is_quantity" checked={form['数量核算']} onChange={e => setForm(f => ({ ...f, 数量核算: e.target.checked }))} />
              <label htmlFor="is_quantity" className="ml-2">数量核算</label>
            </div>
            <div className="flex justify-end space-x-2 mt-4">
              <button type="button" className="px-4 py-2 bg-gray-300 rounded" onClick={closeEdit}>取消</button>
              <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded">保存</button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default SubjectTable; 