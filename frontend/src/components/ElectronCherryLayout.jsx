import React, { useState } from 'react';
import { Bot, MessageSquare, Plus, Search, Settings, User, Home, FileText, BarChart3, Calculator, DollarSign, Receipt, Briefcase, Users, Building, Cog, Sparkles, Minus, Square, X } from 'lucide-react';

const ElectronCherryLayout = ({ children, activeFeature, onFeatureSelect, features, sessions, activeSessionId, onSessionSelect, onNewSession }) => {
  const isElectron = typeof window !== 'undefined' && window.electronAPI;

  // 窗口控制函数
  const handleMinimize = () => {
    if (isElectron) {
      window.electronAPI.minimize();
    }
  };

  const handleMaximize = () => {
    if (isElectron) {
      window.electronAPI.maximize();
    }
  };

  const handleClose = () => {
    if (isElectron) {
      window.electronAPI.close();
    }
  };
  const [assistants, setAssistants] = useState([
    { id: 1, name: 'Default Assistant', avatar: '😊', active: true, lastMessage: '你好，我是智能助手，你可以向我提问任何问题' },
    { id: 2, name: 'Default Assistant', avatar: '😊', active: false, lastMessage: '' }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('assistant');

  const addAssistant = () => {
    const newAssistant = {
      id: Date.now(),
      name: 'Default Assistant',
      avatar: '😊',
      active: false,
      lastMessage: ''
    };
    setAssistants(prev => [...prev, newAssistant]);
  };

  const setActiveAssistant = (id) => {
    setAssistants(prev => prev.map(assistant => ({
      ...assistant,
      active: assistant.id === id
    })));
  };

  // 功能图标映射
  const featureIcons = {
    agent: Bot,
    approval: FileText,
    voucher: Receipt,
    bookkeeping: FileText,
    report: BarChart3,
    settlement: Calculator,
    asset: Briefcase,
    invoice: Receipt,
    cashier: DollarSign,
    salary: Users,
    tax: Calculator,
    subject: FileText,
    roleStaff: Users,
    company: Building,
    settings: Cog
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* 左侧边栏 - Cherry Studio 风格 */}
      <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
        {/* 顶部标签页 */}
        <div className="h-14 flex border-b border-gray-200 bg-white">
          <button 
            onClick={() => setActiveTab('functions')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'functions' 
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50/50' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            功能
          </button>
          <button 
            onClick={() => {
              setActiveTab('assistant');
              onFeatureSelect('agent'); // 激活智能体功能
            }}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'assistant' 
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50/50' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            助手
          </button>
          <button 
            onClick={() => setActiveTab('settings')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'settings' 
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50/50' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            设置
          </button>
        </div>

        {/* 搜索框 - 与右侧状态栏对齐 */}
        <div className="h-14 px-4 bg-white border-b border-gray-200 flex items-center">
          <div className="relative w-full">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder={
                activeTab === 'functions' ? '搜索功能...' : 
                activeTab === 'assistant' ? '搜索助手...' : 
                '搜索设置...'
              }
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50"
            />
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto bg-gray-50/50">
          {activeTab === 'assistant' && (
            <div className="p-2">
              {/* 始终显示会话列表 */}
              {sessions && sessions.map((session) => {
                const lastMessage = session.messages.length > 0 
                  ? session.messages[session.messages.length - 1].content 
                  : '新会话';
                
                return (
                  <div
                    key={session.id}
                    onClick={() => onSessionSelect && onSessionSelect(session.id)}
                    className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                      session.id === activeSessionId 
                        ? 'bg-blue-50 border border-blue-200' 
                        : 'hover:bg-white hover:shadow-sm'
                    }`}
                  >
                    <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center text-lg flex-shrink-0">
                      😊
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {session.name}
                      </div>
                      <div className="text-xs text-gray-400 mt-1 line-clamp-2">
                        {lastMessage}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {activeTab === 'functions' && (
            <div className="p-2">
              {Object.entries(features)
                .filter(([key]) => key !== 'settings' && key !== 'agent') // 过滤掉设置功能和智能体
                .map(([key, feature]) => {
                const IconComponent = featureIcons[key] || FileText;
                return (
                  <div
                    key={key}
                    onClick={() => onFeatureSelect(key)}
                    className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                      activeFeature === key 
                        ? 'bg-blue-50 border border-blue-200 text-blue-700' 
                        : 'hover:bg-white hover:shadow-sm text-gray-700'
                    }`}
                  >
                    <IconComponent size={18} className="flex-shrink-0" />
                    <span className="text-sm font-medium">{feature.name}</span>
                  </div>
                );
              })}
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="p-2">
              <div
                onClick={() => onFeatureSelect('settings')}
                className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                  activeFeature === 'settings' 
                    ? 'bg-blue-50 border border-blue-200 text-blue-700' 
                    : 'hover:bg-white hover:shadow-sm text-gray-700'
                }`}
              >
                <Cog size={18} className="flex-shrink-0" />
                <span className="text-sm font-medium">应用设置</span>
              </div>
            </div>
          )}
        </div>

        {/* 底部添加按钮 */}
        {activeTab === 'assistant' && (
          <div className="p-4 border-t border-gray-200 bg-white">
            <button
              onClick={onNewSession}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors border border-gray-200"
            >
              <Plus size={16} />
              <span>新建对话</span>
            </button>
          </div>
        )}
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        {/* 顶部状态栏 - 类似 Cherry Studio */}
        <div className="h-14 border-b border-gray-200 flex items-center justify-between px-6 bg-white/95 backdrop-blur-sm drag-region">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3 px-3 py-1.5 bg-gray-100 rounded-lg no-drag">
              <Sparkles size={14} className="text-blue-500" />
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-700 font-medium">智能助手</span>
              <span className="text-xs text-gray-400">|</span>
              <span className="text-xs text-gray-500">ERNIE-4.5</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-1 no-drag">
            <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
              <Search size={18} />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
              <Settings size={18} />
            </button>
            
            {/* 窗口控制按钮 */}
            {isElectron && (
              <>
                <div className="w-px h-6 bg-gray-300 mx-2"></div>
                <button
                  onClick={handleMinimize}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  title="最小化"
                >
                  <Minus size={16} />
                </button>
                <button
                  onClick={handleMaximize}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  title="最大化"
                >
                  <Square size={16} />
                </button>
                <button
                  onClick={handleClose}
                  className="p-2 text-gray-400 hover:text-white hover:bg-red-500 rounded-lg transition-colors"
                  title="关闭"
                >
                  <X size={16} />
                </button>
              </>
            )}
          </div>
        </div>

        {/* 主内容 */}
        <div className="flex-1 overflow-hidden">
          {children}
        </div>
      </div>
    </div>
  );
};

export default ElectronCherryLayout;