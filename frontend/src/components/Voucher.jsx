import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, Plus, Minus, Calendar, Paperclip } from 'lucide-react';
import Decimal from 'decimal.js';

// 专业的网格金额单元格组件
const AmountCell = ({ amount, className = "" }) => {
  // 创建12个固定宽度的格子
  const createGridCells = (value) => {
    const cells = [];

    if (!value && value !== 0) {
      // 空单元格 - 12个空格子
      for (let i = 0; i < 12; i++) {
        cells.push(
          <div
            key={i}
            className={`w-6 h-8 border-r border-gray-300 flex items-center justify-center text-xs ${i === 3 || i === 6 ? 'border-r-2 border-gray-500' :
              i === 9 ? 'border-r-2 border-gray-500' :
                i === 11 ? 'border-r-0' : ''
              }`}
          >
            &nbsp;
          </div>
        );
      }
      return cells;
    }

    // 格式化金额
    const formattedAmount = new Decimal(value).toFixed(2);
    const [integerPart, decimalPart] = formattedAmount.split('.');
    const paddedInteger = integerPart.padStart(10, '0');
    const allDigits = paddedInteger + decimalPart;

    for (let i = 0; i < 12; i++) {
      const digit = allDigits[i] || '0';
      const shouldHide = i < 10 && digit === '0' && allDigits.substring(0, i + 1).match(/^0+$/);

      cells.push(
        <div
          key={i}
          className={`w-6 h-8 border-r border-gray-300 flex items-center justify-center text-xs font-mono ${i === 3 || i === 6 ? 'border-r-2 border-gray-500' :
            i === 9 ? 'border-r-2 border-gray-500' :
              i === 11 ? 'border-r-0' : ''
            }`}
        >
          {shouldHide ? '' : digit}
        </div>
      );
    }

    return cells;
  };

  return (
    <td className={`${className} p-0`}>
      <div className="flex">
        {createGridCells(amount)}
      </div>
    </td>
  );
};

const VoucherEntry = ({ index, summary, account, amount, isDebit, balance }) => {
  return (
    <tr className="border-b border-gray-200">
      <td className="py-1 px-2 border-r border-gray-200 text-center w-12 text-sm">{index}</td>
      <td className="py-1 px-2 border-r border-gray-200 text-sm">{summary}</td>
      <td className="py-1 px-2 border-r border-gray-200 text-sm">
        <div className="flex items-center">
          <span className="flex-1">{account}</span>
          {balance && (
            <span className="text-gray-500 text-xs ml-2">
              余额: {new Decimal(balance).toFixed(2)}
            </span>
          )}
        </div>
      </td>
      <AmountCell
        amount={isDebit ? amount : null}
        className="border-r border-gray-200"
      />
      <AmountCell
        amount={!isDebit ? amount : null}
        className=""
      />
    </tr>
  );
};

// 1. 修改 props，接收 editable，默认 false
const Voucher = ({ voucher = {}, index = 0, editable = false }) => {
  // 2. isEditing 只在 editable 为 true 时才允许切换，初始值为 editable
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(editable);

  // 只要 editable 变为 false，isEditing 立即变为 false
  useEffect(() => {
    if (!editable) setIsEditing(false);
  }, [editable]);

  const entries = voucher.entries || [];
  const date = voucher.date || new Date().toISOString().split('T')[0];
  const summary = voucher.summary || '';

  const attachments = voucher.attachments || 0;
  const creator = voucher.creator || '系统';
  const reviewer = voucher.reviewer;

  // 计算借方和贷方的总金额（高精度）
  const totalDebit = entries.reduce((sum, e) => {
    if (e.type === 'debit') {
      return sum.plus(new Decimal(e.amount || 0));
    }
    return sum;
  }, new Decimal(0));

  const totalCredit = entries.reduce((sum, e) => {
    if (e.type === 'credit') {
      return sum.plus(new Decimal(e.amount || 0));
    }
    return sum;
  }, new Decimal(0));



  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* 凭证头部 */}
      <div className="p-4 flex items-center justify-between border-b border-gray-200">
        <div className="flex items-center space-x-6">
          <h3 className="text-xl font-medium">记账凭证</h3>
          <div className="flex items-center space-x-4 text-gray-600">
            <span>{new Date().getFullYear()}年第{index + 1}期</span>
            <div className="flex items-center">
              <Calendar size={16} className="mr-1" />
              <span>{date}</span>
            </div>
            {attachments > 0 && (
              <div className="flex items-center text-blue-500">
                <Paperclip size={16} className="mr-1" />
                <span>附件 {attachments} 张</span>
              </div>
            )}
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-gray-400 hover:text-gray-600 p-1 hover:bg-gray-100 rounded-full transition-colors"
        >
          {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </button>
      </div>

      {/* 凭证主体 */}
      <div className={`overflow-hidden transition-all duration-200 ${isExpanded ? 'max-h-[800px]' : 'max-h-0'}`}>
        <div className="w-full">
          {/* 编辑按钮只在 editable 为 true 且非编辑状态时显示 */}
          {editable && !isEditing && (
            <div className="flex justify-end mb-2">
              <button className="text-blue-500 text-sm" onClick={() => setIsEditing(true)}>编辑</button>
            </div>
          )}
          {/* 编辑区只在 isEditing && editable 时显示 */}
          {isEditing && editable ? (
            <div>编辑区内容（略）</div>
          ) : (
            <table className="w-full border-collapse">
              <tbody>
                {/* 表头行1 - 主标题 */}
                <tr className="bg-gray-50">
                  <td className="py-2 px-2 border-b border-r border-gray-200 text-center w-12 text-sm font-bold">序号</td>
                  <td className="py-2 px-2 border-b border-r border-gray-200 text-center text-sm font-bold">摘要</td>
                  <td className="py-2 px-2 border-b border-r border-gray-200 text-center text-sm font-bold">科目</td>
                  <td className="py-1 px-0 border-b border-r border-gray-200 text-center text-sm font-bold bg-gray-50">
                    <div className="mb-1">借方金额</div>
                  </td>
                  <td className="py-1 px-0 border-b border-gray-200 text-center text-sm font-bold bg-gray-50">
                    <div className="mb-1">贷方金额</div>
                  </td>
                </tr>

                {/* 表头行2 - 单位标识 */}
                <tr className="bg-gray-50">
                  <td className="border-b border-r border-gray-200">&nbsp;</td>
                  <td className="border-b border-r border-gray-200">&nbsp;</td>
                  <td className="border-b border-r border-gray-200">&nbsp;</td>
                  <td className="p-0 border-b border-r border-gray-200">
                    <div className="flex text-xs text-gray-600">
                      <div className="w-6 text-center py-1"></div>
                      <div className="w-6 text-center py-1">亿</div>
                      <div className="w-6 text-center py-1">千</div>
                      <div className="w-6 text-center py-1 border-r-2 border-gray-500">百</div>
                      <div className="w-6 text-center py-1">十</div>
                      <div className="w-6 text-center py-1">万</div>
                      <div className="w-6 text-center py-1 border-r-2 border-gray-500">千</div>
                      <div className="w-6 text-center py-1">百</div>
                      <div className="w-6 text-center py-1">十</div>
                      <div className="w-6 text-center py-1 border-r-2 border-gray-500">元</div>
                      <div className="w-6 text-center py-1">角</div>
                      <div className="w-6 text-center py-1">分</div>
                    </div>
                  </td>
                  <td className="p-0 border-b border-gray-200">
                    <div className="flex text-xs text-gray-600">
                      <div className="w-6 text-center py-1"></div>
                      <div className="w-6 text-center py-1">亿</div>
                      <div className="w-6 text-center py-1">千</div>
                      <div className="w-6 text-center py-1 border-r-2 border-gray-500">百</div>
                      <div className="w-6 text-center py-1">十</div>
                      <div className="w-6 text-center py-1">万</div>
                      <div className="w-6 text-center py-1 border-r-2 border-gray-500">千</div>
                      <div className="w-6 text-center py-1">百</div>
                      <div className="w-6 text-center py-1">十</div>
                      <div className="w-6 text-center py-1 border-r-2 border-gray-500">元</div>
                      <div className="w-6 text-center py-1">角</div>
                      <div className="w-6 text-center py-1">分</div>
                    </div>
                  </td>
                </tr>

                {/* 数据行 */}
                {entries.map((entry, idx) => (
                  <VoucherEntry
                    key={idx}
                    index={idx + 1}
                    summary={summary}
                    account={entry.account || ''}
                    amount={entry.amount || 0}
                    isDebit={entry.type === 'debit'}
                    balance={entry.balance}
                  />
                ))}

                {/* 空行 */}
                {Array.from({ length: Math.max(0, 4 - entries.length) }).map((_, idx) => (
                  <tr key={`empty-${idx}`} className="border-b border-gray-200">
                    <td className="py-1 px-2 border-r border-gray-200 text-center w-12 text-sm">{entries.length + idx + 1}</td>
                    <td className="py-1 px-2 border-r border-gray-200">&nbsp;</td>
                    <td className="py-1 px-2 border-r border-gray-200">&nbsp;</td>
                    <AmountCell className="border-r border-gray-200" />
                    <AmountCell />
                  </tr>
                ))}

                {/* 合计行 */}
                <tr className="bg-gray-50 font-medium border-t-2 border-gray-400">
                  <td className="py-1 px-2 border-r border-gray-200 text-center text-sm font-bold" colSpan="3">
                    合计
                  </td>
                  <AmountCell
                    amount={totalDebit}
                    className="border-r border-gray-200 bg-gray-50"
                  />
                  <AmountCell
                    amount={totalCredit}
                    className="bg-gray-50"
                  />
                </tr>
              </tbody>
            </table>
          )}
        </div>
        {/* 凭证底部 */}
        <div className="p-4 bg-gray-50 text-sm text-gray-600 border-t border-gray-200">
          <div className="flex justify-between">
            <span>制单人：{creator}</span>
            <span>审核人：{reviewer || '待审核'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Voucher;
