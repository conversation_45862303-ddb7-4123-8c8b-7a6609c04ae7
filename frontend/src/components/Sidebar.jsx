import React, { useState, useEffect } from 'react'
import Agent from './Agent'
import Resizer from './Resizer'
import ConversationList from './ConversationList'

const Sidebar = ({ activeFeature, features, aiConfig, setVouchers, setSubjects, setAssets, setStaffs }) => {
  const feature = features[activeFeature]
  const [width, setWidth] = useState(380) // 默认宽度
  const [isResizing, setIsResizing] = useState(false)

  // 会话管理，仅在 agent 功能下使用
  const [sessions, setSessions] = useState(() => [
    { id: Date.now(), name: '新会话', messages: [], isLoading: false }
  ])
  const [activeSessionId, setActiveSessionId] = useState(sessions[0].id)

  const createSession = () => {
    const newSession = { id: Date.now(), name: '新会话', messages: [], isLoading: false }
    setSessions(prev => [...prev, newSession])
    setActiveSessionId(newSession.id)
  }

  const updateSession = (id, updates) => {
    setSessions(prev => prev.map(s => s.id === id ? { ...s, ...updates } : s))
  }

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 确保宽度不超过窗口宽度的70%
      const maxWidth = window.innerWidth * 0.7
      if (width > maxWidth) {
        setWidth(maxWidth)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [width])

  const handleResize = (clientX) => {
    setIsResizing(true)
    // 计算新宽度（考虑左侧功能栏的宽度80px）
    const newWidth = clientX - 80
    // 限制最小宽度和最大宽度
    const constrainedWidth = Math.max(320, Math.min(newWidth, window.innerWidth * 0.7))
    setWidth(constrainedWidth)
  }

  return (
    <div 
      className="relative bg-white border-r border-gray-200 flex flex-col h-full"
      style={{ width: `${width}px`, transition: isResizing ? 'none' : 'width 0.3s ease' }}
      onMouseUp={() => setIsResizing(false)}
    >
      <div className="border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 p-4">{feature.name}</h2>
        {activeFeature === 'agent' && (
          <ConversationList
            sessions={sessions}
            activeId={activeSessionId}
          />
        )}
      </div>
      <div className="flex-1 overflow-hidden">
        {activeFeature === 'agent' ? (
          <div className="h-full">
            {sessions.map(session => (
              <div key={session.id} className={`${session.id === activeSessionId ? 'h-full' : 'hidden'}`} style={{height:'100%'}}>
                <Agent
                  aiConfig={aiConfig}
                  setVouchers={setVouchers}
                  setSubjects={setSubjects}
                  setAssets={setAssets}
                  setStaffs={setStaffs}
                  inSidebar={true}
                  session={session}
                  updateSession={updates => updateSession(session.id, updates)}
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="p-4 space-y-4">
            {activeFeature === 'voucher' && (
              <>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h3 className="font-medium text-sm text-gray-700 mb-2">凭证模板</h3>
                  <p className="text-sm text-gray-600">常用的凭证模板</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h3 className="font-medium text-sm text-gray-700 mb-2">最近使用</h3>
                  <p className="text-sm text-gray-500">暂无最近使用的凭证</p>
                </div>
              </>
            )}
            {/* 其他功能的侧边栏内容可以根据需要添加 */}
          </div>
        )}
      </div>
      <Resizer onResize={handleResize} />
    </div>
  )
}

export default Sidebar
