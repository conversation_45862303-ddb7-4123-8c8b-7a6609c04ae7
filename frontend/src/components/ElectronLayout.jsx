import React, { useState, useEffect } from 'react';
import TitleBar from './TitleBar';
import { Bot, MessageSquare, Plus, Search, Settings, User } from 'lucide-react';

const ElectronLayout = ({ children }) => {
  const [assistants, setAssistants] = useState([
    { id: 1, name: 'Default Assistant', avatar: '😊', active: true },
    { id: 2, name: 'Default Assistant', avatar: '😊', active: false }
  ]);

  const [searchQuery, setSearchQuery] = useState('');

  const addAssistant = () => {
    const newAssistant = {
      id: Date.now(),
      name: 'Default Assistant',
      avatar: '😊',
      active: false
    };
    setAssistants(prev => [...prev, newAssistant]);
  };

  const setActiveAssistant = (id) => {
    setAssistants(prev => prev.map(assistant => ({
      ...assistant,
      active: assistant.id === id
    })));
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* 自定义标题栏 */}
      <TitleBar />
      
      <div className="flex flex-1 overflow-hidden">
        {/* 左侧边栏 - 类似 Cherry Studio */}
        <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
          {/* 顶部导航 */}
          <div className="flex border-b border-gray-200">
            <button className="flex-1 px-4 py-3 text-sm font-medium text-blue-600 border-b-2 border-blue-600 bg-blue-50/50">
              助手
            </button>
            <button className="flex-1 px-4 py-3 text-sm font-medium text-gray-500 hover:text-gray-700">
              话题
            </button>
            <button className="flex-1 px-4 py-3 text-sm font-medium text-gray-500 hover:text-gray-700">
              设置
            </button>
          </div>

          {/* 搜索框 */}
          <div className="p-4 border-b border-gray-200">
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索助手..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* 助手列表 */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-2">
              {assistants.map((assistant) => (
                <div
                  key={assistant.id}
                  onClick={() => setActiveAssistant(assistant.id)}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                    assistant.active 
                      ? 'bg-blue-50 border border-blue-200' 
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center text-lg">
                    {assistant.avatar}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {assistant.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {assistant.id === 1 ? '1' : ''}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 底部添加按钮 */}
          <div className="p-4 border-t border-gray-200">
            <button
              onClick={addAssistant}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <Plus size={16} />
              <span>添加助手</span>
            </button>
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {children}
        </div>
      </div>
    </div>
  );
};

export default ElectronLayout;