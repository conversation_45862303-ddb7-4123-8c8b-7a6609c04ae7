import React, { useState } from 'react';
import { X, Save, TestTube, Settings, Sparkles, Database } from 'lucide-react';
import RAGManagement from './RAGManagement';

const SettingsDialog = ({ isOpen, onClose, onSave, onTest, initialConfig, backendBase }) => {
  const [config, setConfig] = useState(initialConfig || {
    api_key: '',
    base_url: 'https://aistudio.baidu.com/llm/lmapi/v3',
    model: 'ernie-4.5-turbo-vl-preview'
  });
  
  const [testResult, setTestResult] = useState(null);
  const [isTesting, setIsTesting] = useState(false);
  const [showRAGManagement, setShowRAGManagement] = useState(false);

  const handleTest = async () => {
    setIsTesting(true);
    setTestResult(null);
    try {
      const result = await onTest(config);
      setTestResult({ success: true, message: '连接测试成功！' });
    } catch (error) {
      setTestResult({ 
        success: false, 
        message: `连接测试失败: ${error.message || '未知错误'}` 
      });
    } finally {
      setIsTesting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fade-in">
        <div className="bg-white rounded-3xl p-8 w-[480px] max-w-[90vw] shadow-2xl animate-scale-in border border-gray-200">
          {/* 头部 */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white shadow-lg">
                <Settings size={24} />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  AI服务器设置
                </h2>
                <p className="text-sm text-gray-600 mt-1">配置您的AI服务连接</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-200"
            >
              <X size={24} />
            </button>
          </div>

          <div className="space-y-6">
            {/* API Key */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                API Key
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="password"
                value={config.api_key}
                onChange={(e) => setConfig({...config, api_key: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入您的API Key"
              />
            </div>

            {/* 基础URL */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                基础URL
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={config.base_url}
                onChange={(e) => setConfig({...config, base_url: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入API基础URL"
              />
            </div>

            {/* 模型名称 */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                模型名称
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={config.model}
                onChange={(e) => setConfig({...config, model: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入模型名称"
              />
            </div>

            {/* 测试结果 */}
            {testResult && (
              <div className={`p-4 rounded-xl border animate-slide-down ${
                testResult.success
                  ? 'bg-green-50 border-green-200 text-green-800'
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                <div className="flex items-center space-x-2">
                  <Sparkles size={16} className={testResult.success ? 'text-green-600' : 'text-red-600'} />
                  <div className="font-semibold">{testResult.success ? '连接成功' : '连接失败'}</div>
                </div>
                <div className="text-sm mt-1 opacity-90">{testResult.message}</div>
              </div>
            )}

            {/* 按钮组 */}
            <div className="flex space-x-3 pt-4">
              <button
                onClick={() => setShowRAGManagement(true)}
                className="flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
              >
                <Database size={18} className="mr-2" />
                RAG管理
              </button>
              
              <button
                onClick={handleTest}
                disabled={isTesting}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {isTesting ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>测试中</span>
                  </div>
                ) : (
                  <>
                    <TestTube size={18} className="mr-2" />
                    测试连接
                  </>
                )}
              </button>
              
              <button
                onClick={() => onSave(config)}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
              >
                <Save size={18} className="mr-2" />
                保存设置
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* RAG管理对话框 */}
      <RAGManagement
        isOpen={showRAGManagement}
        onClose={() => setShowRAGManagement(false)}
        backendBase={backendBase}
        aiConfig={config}
      />
    </>
  );
};

// 保留SettingsDialog本身内容，不包含RoleStaffConfig组件实现。
export default SettingsDialog;
