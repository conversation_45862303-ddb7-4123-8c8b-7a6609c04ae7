from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, AsyncGenerator
from datetime import date
from core.db import AsyncSessionLocal, engine
from sqlalchemy.ext.asyncio import AsyncSession
from models.company import Company
from crud.company import get_companies, get_company, create_company, update_company, delete_company
import asyncio

router = APIRouter()

# Pydantic models for request/response
class CompanyBase(BaseModel):
    name: str
    business_scope: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    tax_id: Optional[str] = None
    accounting_standards: Optional[str] = None
    established_date: Optional[date] = None
    registered_capital: Optional[float] = None
    status: Optional[str] = "active"

class CompanyCreate(CompanyBase):
    pass

class CompanyUpdate(CompanyBase):
    pass

class CompanyInDB(CompanyBase):
    id: int
    
    class Config:
        from_attributes = True

# Dependency to get database session
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal(bind=engine) as db:
        yield db

@router.get("/companies", response_model=List[CompanyInDB])
async def get_companies_api(skip: int = 0, limit: int = 100, db=Depends(get_db)):
    companies = await get_companies(db, skip=skip, limit=limit)
    return companies

@router.get("/companies/{company_id}", response_model=CompanyInDB)
async def get_company_api(company_id: int, db=Depends(get_db)):
    company = await get_company(db, company_id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")
    return company

@router.post("/companies", response_model=CompanyInDB)
async def create_company_api(company: CompanyCreate, db=Depends(get_db)):
    db_company = Company(**company.dict())
    result = await create_company(db, db_company)
    return result

@router.put("/companies/{company_id}", response_model=CompanyInDB)
async def update_company_api(company_id: int, company: CompanyUpdate, db=Depends(get_db)):
    db_company = await get_company(db, company_id)
    if not db_company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    # Update the company with new data
    update_data = company.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_company, key, value)
    
    result = await update_company(db, db_company)
    return result

@router.delete("/companies/{company_id}")
async def delete_company_api(company_id: int, db=Depends(get_db)):
    db_company = await get_company(db, company_id)
    if not db_company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    await delete_company(db, db_company)
    return {"success": True, "message": "Company deleted successfully"}