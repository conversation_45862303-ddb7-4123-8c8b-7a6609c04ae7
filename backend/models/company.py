from sqlalchemy import Column, String, Text, Date, DateTime, DECIMAL, Integer
from core.db import Base
from datetime import datetime

class Company(Base):
    __tablename__ = "companies"
    
    # Basic company information
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False, comment="公司名称")
    business_scope = Column(Text, nullable=True, comment="经营范围")
    
    # Industry and classification
    industry = Column(String, nullable=True, comment="行业")
    company_size = Column(String, nullable=True, comment="公司规模")
    
    # Tax and accounting information
    tax_id = Column(String, nullable=True, comment="税号")
    accounting_standards = Column(String, nullable=True, comment="会计准则")
    
    # Additional metadata
    established_date = Column(Date, nullable=True, comment="成立日期")
    registered_capital = Column(DECIMAL(18, 2), nullable=True, comment="注册资本")
    status = Column(String, default="active", comment="状态")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")