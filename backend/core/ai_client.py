import logging
from typing import Dict, Optional, Callable
import json
import aiohttp
from core.prompt_builder import PromptBuilder
from core.config import config
from core.llm_retry import get_retry_handler

logger = logging.getLogger(__name__)

class AIClient:
    """AI 客户端，支持 OpenAI 兼容接口"""
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None):
        self.api_key = api_key or config.LLM_API_KEY
        self.base_url = base_url or config.LLM_BASE_URL
        self.model = model or config.LLM_MODEL
        self.prompt_builder = PromptBuilder()

    def update_config(self, api_key: Optional[str], base_url: Optional[str] = None, model: Optional[str] = None):
        self.api_key = api_key
        if base_url is not None:
            self.base_url = base_url
        if model is not None:
            self.model = model

    def is_configured(self) -> bool:
        return self.api_key is not None and self.base_url is not None

    async def analyze_receipt_content(self, content: str, prompt_template: Optional[str] = None, stream_callback: Optional[Callable] = None, user_id: Optional[str] = None) -> Dict:
        if not self.is_configured():
            raise Exception("AI 客户端未配置，请先设置 API KEY")
        if prompt_template:
            prompt = prompt_template.replace("{content}", content)
        else:
            prompt = self.prompt_builder.build_prompt(content, user_id=user_id)
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        data = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "你是一个专业的会计助手，能够根据用户提供的文本或图片内容生成会计凭证。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": config.LLM_TEMPERATURE,
            "top_p": config.LLM_TOP_P,
            "stream": bool(stream_callback),
            "enable_thinking": True if stream_callback else False
        }
        retry_handler = get_retry_handler()
        
        async def _make_request():
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/chat/completions", headers=headers, json=data) as response:
                    if response.status != 200:
                        error_msg = await response.text()
                        raise Exception(f"AI API 调用失败: {error_msg}")
                    if stream_callback:
                        async for line in response.content:
                            if not line:
                                continue
                            try:
                                decoded = line.decode("utf-8").strip()
                            except UnicodeDecodeError:
                                continue
                            if not decoded.startswith("data:"):
                                continue
                            payload = decoded[5:].strip()
                            if payload == "[DONE]":
                                break
                            try:
                                payload_json = json.loads(payload)
                                delta = payload_json.get("choices", [{}])[0].get("delta", {})
                                content_piece = delta.get("content", "")
                                if content_piece:
                                    stream_callback(content_piece)
                            except Exception as ie:
                                logger.debug(f"解析流式数据时出错: {str(ie)}，原始数据: {payload}")
                                continue
                        return {"success": True, "stream": True}
                    else:
                        result = await response.json()
                        ai_content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                        return {"success": True, "raw_response": ai_content, "error": None}
        
        try:
            return await retry_handler.execute_with_retry(_make_request)
        except Exception as e:
            logger.error(f"AI 分析出错（重试后仍然失败）: {str(e)}")
            return {"success": False, "raw_response": None, "error": str(e)}

# 全局 AI 客户端实例
_ai_client = None

def get_ai_client(api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None) -> AIClient:
    global _ai_client
    if _ai_client is None:
        _ai_client = AIClient(api_key, base_url, model)
    else:
        _ai_client.update_config(api_key, base_url, model)
    return _ai_client 