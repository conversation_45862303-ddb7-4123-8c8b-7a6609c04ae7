from typing import Dict, Any, List, Optional
from datetime import datetime

# 导入各模块的内存数据
# 移除 from api.subject import subject_accounts
# 如需科目表数据请通过数据库异步查询获取
from api.asset import assets
from api.role_staff import staffs
from api.experience import get_experience

class PromptBuilder:
    def __init__(self):
        pass

    def parse_user_input(self, user_input: str) -> Dict[str, Any]:
        """
        解析用户输入，提取关键信息。
        目前为占位实现，后续可扩展为 NLP/正则/关键词等多种方式。
        """
        # TODO: 实现更智能的解析逻辑
        return {"raw_input": user_input}

    def gather_context_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        聚合科目表、资产表、员工表、经验知识等上下文数据。
        目前直接读取内存数据，经验知识按 user_id 聚合。
        """
        experience = get_experience(user_id, limit=5) if user_id else []
        return {
            "subjects": [], # 科目表数据已移除，此处返回空列表
            "assets": [a.dict() for a in assets],
            "staff": [s.dict() for s in staffs],
            "experience": experience
        }

    def build_prompt(self, user_input: str, user_id: Optional[str] = None) -> str:
        parsed_input = self.parse_user_input(user_input)
        context = self.gather_context_data(user_id)
        current_date = datetime.now().strftime("%Y-%m-%d")
        prompt = (
            f"你是企业的会计助手。\n"
            f"当前科目表：{context['subjects']}\n"
            f"资产表：{context['assets']}\n"
            f"员工表：{context['staff']}\n"
            f"历史经验：{context['experience']}\n"
            f"用户输入：{parsed_input['raw_input']}\n"
            f"请根据上述信息生成最合适的会计凭证、科目、资产、员工等建议。\n"
            f"重要：请严格按照以下顺序生成卡片，确保依赖关系正确：\n"
            f"1. 科目卡片（如需要新建科目）\n"
            f"2. 资产卡片（如需要新建资产）\n"
            f"3. 员工卡片（如需要新建员工）\n"
            f"4. 凭证卡片（引用已创建的科目和资产）\n"
            f"日期处理规则：如果用户只提供'日'，请自动补充当前年月，格式为'YYYY-MM-DD'。当前日期为{current_date}。例如：用户说'22日'，请返回'{current_date.split('-')[0]}-{current_date.split('-')[1]}-22'。\n"
            f"请判断每一项是否需要新建，返回时请包含op字段（create/skip/update），格式如下：\n"
            f"科目示例：\n"
            f"{{'type': 'subject', 'op': 'create', 'data': {{ '科目编码': '1601', '科目名称': '固定资产-电子设备', '类别': '资产', '方向': '借', '备注': '用于记录企业电子设备的固定资产' }} }}\n"
            f"资产示例：\n"
            f"{{'type': 'asset', 'op': 'create', 'data': {{ '资产编码': 'A001', '资产名称': '电脑', '类别': 'IT', '原值': 10000, '净值': 8000, '购置日期': '{current_date}', '使用年限': '5', '状态': '在用', '备注': '' }} }}\n"
            f"员工示例：\n"
            f"{{'type': 'staff', 'op': 'create', 'data': {{ '工号': 'S001', '姓名': '张三', '岗位编码': 'manager', '电话': '123456', '状态': '在职', '备注': '' }} }}\n"
            f"凭证示例（引用已创建的科目）：\n"
            f"{{'type': 'voucher', 'op': 'create', 'data': {{ '日期': '{current_date}', '摘要': '采购电脑', '借方': [{{'科目名称': '固定资产-电子设备', '科目编码': '1601', '金额': 10000}}], '贷方': [{{'科目名称': '银行存款', '科目编码': '1002', '金额': 10000}}] }} }}\n"
            f"如果不需要新建，op请返回'skip'，无需data字段。所有内容请以JSON格式返回，不要输出多余解释性文字。\n"
            f"注意：科目卡片请务必返回所有必填字段（科目编码、科目名称、类别、方向、备注），如缺失请自动补全或建议用户填写。\n"
            f"所有JSON字段名必须为中文（如科目编码、科目名称、类别、方向、备注、资产名称、原值、净值、购置日期、使用年限、状态、摘要、日期、借方、贷方、工号、姓名、岗位编码、电话等），不能出现任何英文字段名，否则系统无法识别。\n"
            f"凭证卡片必须引用已创建的科目编码，确保科目存在后再创建凭证。\n"
            f"注意：对于大额资产（如电脑、车辆、设备等），建议创建专门的科目进行管理，科目编码可以按资产类型细分，如160101（固定资产-电子设备-电脑）、160102（固定资产-电子设备-打印机）等。\n"
            f"\n"
            f"【重要补充】\n"
            f"1. 科目卡片生成时，务必保证科目编码长度与名称层级严格一致（如1601-固定资产，160101-固定资产-电子设备，16010101-固定资产-电子设备-电脑）。\n"
            f"2. 如需新建三级及以上科目，必须同时返回所有缺失的上级科目。\n"
            f"3. 返回格式为数组，每个科目包含“科目编码”“科目名称”“类别”“方向”等字段。\n"
        )
        return prompt 