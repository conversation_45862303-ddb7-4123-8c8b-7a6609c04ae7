"""
配置管理模块
"""

import os
import logging
from typing import Optional
from pathlib import Path

logger = logging.getLogger(__name__)

# 尝试加载环境变量
try:
    from dotenv import load_dotenv
    
    # 查找.env文件
    env_path = Path(__file__).parent.parent / ".env"
    if env_path.exists():
        load_dotenv(env_path)
        logger.info(f"已加载环境变量文件: {env_path}")
    else:
        logger.info("未找到.env文件，使用系统环境变量")
except ImportError:
    logger.warning("python-dotenv未安装，无法加载.env文件")


class Config:
    """配置类"""
    
    # 网络搜索配置
    SERPER_API_KEY: Optional[str] = os.getenv("SERPER_API_KEY")
    
    # 天气API配置
    OPENWEATHER_API_KEY: Optional[str] = os.getenv("OPENWEATHER_API_KEY")
    
    # OpenAI API配置
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    OPENAI_BASE_URL: str = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    
    # 嵌入模型配置
    SENTENCE_TRANSFORMER_MODEL: str = os.getenv("SENTENCE_TRANSFORMER_MODEL", "paraphrase-multilingual-MiniLM-L12-v2")
    
    # LLM配置
    LLM_API_KEY: Optional[str] = os.getenv("LLM_API_KEY")
    LLM_BASE_URL: str = os.getenv("LLM_BASE_URL", "https://api.openai.com/v1")
    LLM_MODEL: str = os.getenv("LLM_MODEL", "gpt-3.5-turbo")
    LLM_TEMPERATURE: float = float(os.getenv("LLM_TEMPERATURE", "0.2"))
    LLM_TOP_P: float = float(os.getenv("LLM_TOP_P", "0.8"))
    
    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./app.db")
    
    # 向量数据库配置
    CHROMA_PERSIST_DIRECTORY: str = os.getenv("CHROMA_PERSIST_DIRECTORY", "./chroma_db")
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # 文件上传配置
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "10"))  # MB
    
    # 会话配置
    SESSION_TIMEOUT: int = int(os.getenv("SESSION_TIMEOUT", "24"))  # 小时
    
    # 上传目录
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "./uploaded_files")
    
    @classmethod
    def get_upload_dir(cls) -> Path:
        """获取上传目录路径"""
        upload_dir = Path(cls.UPLOAD_DIR)
        upload_dir.mkdir(exist_ok=True)
        return upload_dir
    
    @classmethod
    def get_chroma_dir(cls) -> Path:
        """获取Chroma数据库目录路径"""
        chroma_dir = Path(cls.CHROMA_PERSIST_DIRECTORY)
        chroma_dir.mkdir(exist_ok=True)
        return chroma_dir
    
    @classmethod
    def is_search_enabled(cls) -> bool:
        """检查是否启用网络搜索"""
        return cls.SERPER_API_KEY is not None
    
    @classmethod
    def is_weather_enabled(cls) -> bool:
        """检查是否启用天气查询"""
        return cls.OPENWEATHER_API_KEY is not None
    
    @classmethod
    def is_openai_enabled(cls) -> bool:
        """检查是否启用OpenAI服务"""
        return cls.OPENAI_API_KEY is not None
    
    @classmethod
    def is_llm_enabled(cls) -> bool:
        """检查是否启用LLM服务"""
        return cls.LLM_API_KEY is not None
    
    @classmethod
    def setup_logging(cls):
        """设置日志配置"""
        # 注意：此方法已被移动到 main.py 中统一配置，避免重复日志
        # 日志配置现在在 main.py 中使用 colorlog 进行
        pass


# 全局配置实例
config = Config()

# 注意：日志配置已移动到 main.py 中，避免重复配置
# config.setup_logging()
