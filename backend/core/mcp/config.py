"""
MCP (Model Context Protocol) 客户端配置
"""
import logging
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class MCPTransportType(Enum):
    """MCP 传输类型"""
    STDIO = "stdio"
    SSE = "sse"
    STREAMABLE_HTTP = "streamable-http"

@dataclass
class MCPServerConfig:
    """MCP 服务器配置"""
    name: str
    transport_type: MCPTransportType = MCPTransportType.STDIO
    # stdio 配置
    command: Optional[str] = None
    args: List[str] = None
    # http/sse 配置
    url: Optional[str] = None
    # 通用配置
    env: Dict[str, str] = None
    disabled: bool = False
    auto_approve: List[str] = None
    # 连接配置
    timeout: int = 30
    retry_count: int = 3
    
    def __post_init__(self):
        if self.args is None:
            self.args = []
        if self.env is None:
            self.env = {}
        if self.auto_approve is None:
            self.auto_approve = []
        
        # 验证配置
        if self.transport_type == MCPTransportType.STDIO:
            if not self.command:
                raise ValueError("stdio 传输需要指定 command")
        elif self.transport_type in [MCPTransportType.SSE, MCPTransportType.STREAMABLE_HTTP]:
            if not self.url:
                raise ValueError(f"{self.transport_type.value} 传输需要指定 url")

@dataclass
class MCPTool:
    """MCP 工具定义"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    server_name: str

@dataclass
class MCPServerStatus:
    """MCP 服务器状态"""
    name: str
    status: str  # "connected", "disconnected", "error", "disabled"
    transport_type: MCPTransportType = MCPTransportType.STDIO
    pid: Optional[int] = None
    connection_info: Optional[str] = None
    error_message: Optional[str] = None
    tools: List[MCPTool] = None
    
    def __post_init__(self):
        if self.tools is None:
            self.tools = []