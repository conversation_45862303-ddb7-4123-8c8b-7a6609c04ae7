"""
基于 FastMCP 的 MCP 客户端实现
使用 FastMCP 的官方客户端来替代自定义实现
"""
import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import asdict

import aiohttp
from fastmcp import Client

from .config import MCPTransportType, MCPServerConfig, MCPTool, MCPServerStatus

logger = logging.getLogger(__name__)

class FastMCPClientWrapper:
    """FastMCP 客户端包装器"""
    
    def __init__(self):
        self.servers: Dict[str, MCPServerConfig] = {}
        self.clients: Dict[str, Client] = {}
        self.tools: Dict[str, MCPTool] = {}
        self.server_status: Dict[str, MCPServerStatus] = {}
    
    def add_server(self, config: MCPServerConfig):
        """添加 MCP 服务器配置"""
        self.servers[config.name] = config
        self.server_status[config.name] = MCPServerStatus(
            name=config.name,
            status="disconnected",
            transport_type=config.transport_type
        )
    
    async def start_server(self, server_name: str) -> bool:
        """启动 MCP 服务器"""
        if server_name not in self.servers:
            logger.error(f"未找到服务器配置: {server_name}")
            return False
        
        server_config = self.servers[server_name]
        
        try:
            # 停止已存在的连接
            await self.stop_server(server_name)
            
            # 创建 FastMCP 客户端
            if server_config.transport_type == MCPTransportType.SSE:
                # 对于 SSE 传输，使用 HTTP URL
                client = Client(server_config.url)
            else:
                logger.error(f"不支持的传输类型: {server_config.transport_type}")
                return False

            # 尝试连接 - FastMCP 使用上下文管理器
            try:
                # 测试连接
                async with client:
                    # 测试基本连接
                    tools = await client.list_tools()
                    logger.debug(f"连接测试成功，工具数量: {len(tools) if hasattr(tools, '__len__') else 'unknown'}")

                # 连接成功，保存客户端
                self.clients[server_name] = client
                
                self.server_status[server_name] = MCPServerStatus(
                    name=server_name,
                    status="connected",
                    transport_type=server_config.transport_type,
                    connection_info=server_config.url
                )
                
                # 获取工具列表
                await self._discover_tools(server_name)
                
                logger.info(f"FastMCP 服务器启动成功: {server_name}")
                return True
                
            except Exception as e:
                logger.error(f"FastMCP 连接失败: {e}")
                error_msg = f"连接失败: {str(e)}"
                self.server_status[server_name] = MCPServerStatus(
                    name=server_name,
                    status="error",
                    transport_type=server_config.transport_type,
                    error_message=error_msg
                )
                return False
                
        except Exception as e:
            error_msg = f"启动服务器时发生异常: {str(e)}"
            self.server_status[server_name] = MCPServerStatus(
                name=server_name,
                status="error",
                transport_type=server_config.transport_type,
                error_message=error_msg
            )
            logger.error(f"启动 FastMCP 服务器失败: {server_name}, {error_msg}")
            return False
    
    async def stop_server(self, server_name: str) -> bool:
        """停止 MCP 服务器"""
        if server_name in self.clients:
            try:
                client = self.clients[server_name]
                await client.close()
                del self.clients[server_name]
                logger.info(f"FastMCP 服务器已停止: {server_name}")

            except Exception as e:
                logger.error(f"停止 FastMCP 服务器失败: {server_name}, {e}")
                return False
        
        # 更新状态
        if server_name in self.server_status:
            self.server_status[server_name].status = "disconnected"
            self.server_status[server_name].connection_info = None
            
        # 清理工具
        tools_to_remove = [name for name, tool in self.tools.items() 
                          if tool.server_name == server_name]
        for tool_name in tools_to_remove:
            del self.tools[tool_name]
            
        return True
    
    async def _discover_tools(self, server_name: str):
        """发现服务器工具"""
        try:
            client = self.clients[server_name]
            tools = []

            # 使用 FastMCP 客户端获取工具列表
            try:
                async with client:
                    tools_response = await client.list_tools()
                    logger.debug(f"FastMCP 工具列表响应: {tools_response}")

                    if hasattr(tools_response, 'tools'):
                        # 标准 MCP 响应格式
                        for tool_data in tools_response.tools:
                            tool = MCPTool(
                                name=tool_data.name,
                                description=tool_data.description or "",
                                input_schema=tool_data.inputSchema or {},
                                server_name=server_name
                            )
                            tools.append(tool)
                            self.tools[tool.name] = tool

                    elif isinstance(tools_response, list):
                        # 列表格式响应
                        for tool_data in tools_response:
                            if hasattr(tool_data, 'name'):
                                # 对象格式
                                tool = MCPTool(
                                    name=tool_data.name,
                                    description=getattr(tool_data, 'description', ''),
                                    input_schema=getattr(tool_data, 'inputSchema', {}),
                                    server_name=server_name
                                )
                            elif isinstance(tool_data, dict):
                                # 字典格式
                                tool = MCPTool(
                                    name=tool_data['name'],
                                    description=tool_data.get('description', ''),
                                    input_schema=tool_data.get('inputSchema', {}),
                                    server_name=server_name
                                )
                            else:
                                continue
                            tools.append(tool)
                            self.tools[tool.name] = tool

                    elif isinstance(tools_response, dict) and 'tools' in tools_response:
                        # 字典格式响应
                        for tool_data in tools_response['tools']:
                            tool = MCPTool(
                                name=tool_data['name'],
                                description=tool_data.get('description', ''),
                                input_schema=tool_data.get('inputSchema', {}),
                                server_name=server_name
                            )
                            tools.append(tool)
                            self.tools[tool.name] = tool

                    else:
                        logger.warning(f"未知的工具列表响应格式: {type(tools_response)}")

            except Exception as e:
                logger.error(f"获取工具列表失败: {e}")
                # 如果工具发现失败，为城市服务器添加预定义工具
                if server_name == "city":
                    predefined_tools = [
                        {
                            "name": "get_city_tier",
                            "description": "根据城市名称查询城市属于哪一线城市",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "city_name": {
                                        "type": "string",
                                        "description": "要查询的城市名称"
                                    }
                                },
                                "required": ["city_name"]
                            }
                        },
                        {
                            "name": "search_cities",
                            "description": "根据关键词搜索城市",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "keyword": {
                                        "type": "string",
                                        "description": "搜索关键词"
                                    }
                                },
                                "required": ["keyword"]
                            }
                        },
                        {
                            "name": "list_cities_by_tier",
                            "description": "列出指定分级的所有城市",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "tier": {
                                        "type": "string",
                                        "description": "城市分级"
                                    }
                                },
                                "required": ["tier"]
                            }
                        },
                        {
                            "name": "get_all_tiers",
                            "description": "获取所有城市分级的统计信息",
                            "inputSchema": {
                                "type": "object",
                                "properties": {},
                                "required": []
                            }
                        }
                    ]
                    
                    for tool_data in predefined_tools:
                        tool = MCPTool(
                            name=tool_data["name"],
                            description=tool_data["description"],
                            input_schema=tool_data["inputSchema"],
                            server_name=server_name
                        )
                        tools.append(tool)
                        self.tools[tool.name] = tool
            
            # 更新服务器状态
            self.server_status[server_name].tools = tools
            logger.info(f"发现 {len(tools)} 个工具来自服务器: {server_name}")
            
        except Exception as e:
            logger.error(f"发现工具失败 {server_name}: {e}")
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用 MCP 工具"""
        if tool_name not in self.tools:
            raise ValueError(f"未找到工具: {tool_name}")
            
        tool = self.tools[tool_name]
        server_name = tool.server_name
        
        if server_name not in self.clients:
            raise RuntimeError(f"服务器未连接: {server_name}")
        
        try:
            client = self.clients[server_name]

            # 使用 FastMCP 客户端调用工具
            async with client:
                response = await client.call_tool(tool_name, parameters)

                logger.debug(f"FastMCP 工具调用响应: {response}")

                # 处理 FastMCP 响应
                if hasattr(response, 'content') and hasattr(response, 'data'):
                    # FastMCP CallToolResult 对象
                    # 使用 data 属性获取文本内容
                    content_text = response.data if response.data else ""

                    # 如果有 structured_content，优先使用
                    if hasattr(response, 'structured_content') and response.structured_content:
                        if isinstance(response.structured_content, dict) and 'result' in response.structured_content:
                            content_text = response.structured_content['result']

                    return {
                        "success": True,
                        "result": {"content": content_text},
                        "tool_name": tool_name
                    }
                elif hasattr(response, 'content'):
                    # 其他有 content 属性的响应对象
                    content_list = response.content
                    if isinstance(content_list, list) and len(content_list) > 0:
                        # 提取第一个内容项的文本
                        first_content = content_list[0]
                        if hasattr(first_content, 'text'):
                            content_text = first_content.text
                        else:
                            content_text = str(first_content)
                    else:
                        content_text = str(content_list)

                    return {
                        "success": True,
                        "result": {"content": content_text},
                        "tool_name": tool_name
                    }
                elif isinstance(response, list) and len(response) > 0:
                    # 列表响应，取第一个元素
                    first_response = response[0]
                    if hasattr(first_response, 'content'):
                        return {
                            "success": True,
                            "result": {"content": first_response.content},
                            "tool_name": tool_name
                        }
                    elif hasattr(first_response, 'text'):
                        return {
                            "success": True,
                            "result": {"content": first_response.text},
                            "tool_name": tool_name
                        }
                    else:
                        return {
                            "success": True,
                            "result": {"content": str(first_response)},
                            "tool_name": tool_name
                        }
                elif isinstance(response, dict):
                    # 字典响应
                    if "result" in response:
                        return {
                            "success": True,
                            "result": response["result"],
                            "tool_name": tool_name
                        }
                    elif "error" in response:
                        return {
                            "success": False,
                            "error": response["error"],
                            "tool_name": tool_name
                        }
                    else:
                        return {
                            "success": True,
                            "result": response,
                            "tool_name": tool_name
                        }
                elif isinstance(response, str):
                    # 字符串响应
                    return {
                        "success": True,
                        "result": {"content": response},
                        "tool_name": tool_name
                    }
                else:
                    # 其他类型响应
                    return {
                        "success": True,
                        "result": {"content": str(response)},
                        "tool_name": tool_name
                    }
                
        except Exception as e:
            logger.error(f"FastMCP 工具调用失败: {tool_name}, {e}")
            raise
    
    async def start_all_servers(self):
        """启动所有未禁用的服务器"""
        tasks = []
        for server_name in self.servers:
            if not self.servers[server_name].disabled:
                tasks.append(self.start_server(server_name))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if r is True)
            logger.info(f"启动了 {success_count}/{len(tasks)} 个 FastMCP 服务器")
    
    async def stop_all_servers(self):
        """停止所有服务器"""
        tasks = []
        for server_name in list(self.clients.keys()):
            tasks.append(self.stop_server(server_name))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            logger.info("所有 FastMCP 服务器已停止")
    
    def get_server_status(self, server_name: str = None) -> Dict[str, Any]:
        """获取服务器状态"""
        if server_name:
            if server_name in self.server_status:
                return asdict(self.server_status[server_name])
            else:
                return {"name": server_name, "status": "not_configured"}
        else:
            return {name: asdict(status) for name, status in self.server_status.items()}
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取所有可用工具"""
        return [asdict(tool) for tool in self.tools.values()]

# 全局 FastMCP 客户端实例
fastmcp_client = FastMCPClientWrapper()
