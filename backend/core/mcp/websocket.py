"""
MCP (Model Context Protocol) WebSocket 传输连接实现
"""
import asyncio
import json
import logging
from typing import Dict, Any

import websockets

from .base import MCPConnection

logger = logging.getLogger(__name__)

class MCPWebSocketConnection(MCPConnection):
    """WebSocket 传输连接"""
    
    def __init__(self, config):
        super().__init__(config)
        self.websocket: websockets.WebSocketClientProtocol = None
        
    async def connect(self) -> bool:
        """连接到 WebSocket 端点"""
        try:
            self.websocket = await websockets.connect(self.config.url)
            self.connected = True
            logger.info(f"WebSocket MCP 服务器连接成功: {self.config.url}")
            return True
        except Exception as e:
            logger.error(f"WebSocket 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开 WebSocket 连接"""
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
        self.connected = False
    
    async def send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送 WebSocket 请求"""
        if not self.connected or not self.websocket:
            raise RuntimeError("连接未建立")
        
        request = {
            "jsonrpc": "2.0",
            "id": str(asyncio.get_event_loop().time()),
            "method": method,
            "params": params or {}
        }
        
        try:
            await self.websocket.send(json.dumps(request))
            response_str = await self.websocket.recv()
            response = json.loads(response_str)
            return response
        except Exception as e:
            logger.error(f"WebSocket 请求失败: {e}")
            raise