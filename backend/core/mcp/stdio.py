"""
MCP (Model Context Protocol) STDIO 传输连接实现
"""
import asyncio
import json
import logging
import subprocess
import os
from typing import Dict, Any

from .base import MCPConnection

logger = logging.getLogger(__name__)

class MCPStdioConnection(MCPConnection):
    """stdio 传输连接"""
    
    def __init__(self, config):
        super().__init__(config)
        self.process: subprocess.Popen = None
        
    async def connect(self) -> bool:
        """启动 stdio 进程"""
        try:
            env = os.environ.copy()
            env.update(self.config.env)
            
            cmd = [self.config.command] + self.config.args
            logger.info(f"启动 stdio MCP 服务器: {' '.join(cmd)}")
            
            self.process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                text=True
            )
            
            # 等待进程启动
            await asyncio.sleep(1)
            
            if self.process.poll() is None:
                self.connected = True
                logger.info(f"stdio MCP 服务器启动成功 (PID: {self.process.pid})")
                return True
            else:
                stderr = self.process.stderr.read() if self.process.stderr else ""
                raise RuntimeError(f"进程启动后立即退出: {stderr}")
                
        except Exception as e:
            logger.error(f"启动 stdio MCP 服务器失败: {e}")
            return False
    
    async def disconnect(self):
        """停止 stdio 进程"""
        if self.process:
            try:
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.process.wait()
            except Exception as e:
                logger.error(f"停止 stdio 进程失败: {e}")
            finally:
                self.process = None
                self.connected = False
    
    async def send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送 JSON-RPC 请求"""
        if not self.connected or not self.process:
            raise RuntimeError("连接未建立")
        
        request = {
            "jsonrpc": "2.0",
            "id": str(asyncio.get_event_loop().time()),
            "method": method,
            "params": params or {}
        }
        
        try:
            # 发送请求
            request_json = json.dumps(request) + "\n"
            self.process.stdin.write(request_json)
            self.process.stdin.flush()
            
            # 读取响应
            response_line = self.process.stdout.readline()
            if not response_line:
                raise RuntimeError("未收到响应")
            
            response = json.loads(response_line.strip())
            return response
            
        except Exception as e:
            logger.error(f"stdio 请求失败: {e}")
            raise