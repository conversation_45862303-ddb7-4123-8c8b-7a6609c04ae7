"""
MCP (Model Context Protocol) 客户端基础连接类
"""
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class MCPConnection:
    """MCP 连接基类"""
    
    def __init__(self, config):
        self.config = config
        self.connected = False
        
    async def connect(self) -> bool:
        """连接到服务器"""
        raise NotImplementedError
        
    async def disconnect(self):
        """断开连接"""
        raise NotImplementedError
        
    async def send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送请求"""
        raise NotImplementedError