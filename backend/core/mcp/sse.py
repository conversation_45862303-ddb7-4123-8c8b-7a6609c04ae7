"""
MCP (Model Context Protocol) SSE 传输连接实现
"""
import asyncio
import json
import logging
import urllib.parse
from typing import Dict, Any, Optional

import aiohttp

from .base import MCPConnection

logger = logging.getLogger(__name__)

class MCPSSEConnection(MCPConnection):
    """SSE 传输连接"""
    
    def __init__(self, config):
        super().__init__(config)
        self.session: aiohttp.ClientSession = None
        self.base_url: str = None
        self.session_endpoint: str = None
        self._retry_count = 0
        self._initialized = False
        
    async def connect(self) -> bool:
        """连接到 SSE 端点"""
        try:
            self.session = aiohttp.ClientSession()
            
            # 检测端点
            await self._detect_endpoints()
            
            if self.connected:
                logger.info(f"SSE MCP 服务器连接成功: {self.config.url}")
                return True
            else:
                logger.error(f"SSE MCP 服务器连接失败: {self.config.url}")
                return False
                
        except Exception as e:
            logger.error(f"SSE 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开 SSE 连接"""
        if self.session:
            await self.session.close()
            self.session = None
        self.connected = False
        self.base_url = None
        self.session_endpoint = None
        self._retry_count = 0
        self._initialized = False
    
    async def _extract_endpoint_from_sse(self, response) -> Optional[str]:
        """从 SSE 流中提取会话端点"""
        try:
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                
                if line_str.startswith('event: endpoint'):
                    # 下一行应该是 data
                    try:
                        next_line = await response.content.readline()
                        if next_line:
                            next_line_str = next_line.decode('utf-8').strip()
                            if next_line_str.startswith('data: '):
                                endpoint_url = next_line_str[6:]  # 去掉 'data: ' 前缀
                                if endpoint_url.strip():
                                    return endpoint_url
                    except:
                        continue
                
                # 如果遇到空行，可能是事件结束
                elif not line_str:
                    continue
                    
                # 如果遇到 ping 事件，继续
                elif line_str.startswith(': ping'):
                    continue
                    
                # 如果遇到其他事件，继续
                elif line_str.startswith('event: '):
                    continue
                    
                # 如果遇到数据，但不是端点事件，继续
                elif line_str.startswith('data: '):
                    continue
        except Exception as e:
            logger.debug(f"提取会话端点时发生错误: {e}")
            
        return None
    
    async def _detect_endpoints(self):
        """智能检测 MCP 服务器端点"""
        # 解析 URL
        parsed = urllib.parse.urlparse(self.config.url)
        
        # 构建 SSE 端点 URL
        sse_url = self.config.url
        
        # FastMCP 服务器通常只提供 SSE 端点，没有健康检查端点
        # 直接尝试连接到 SSE 端点
        try:
            logger.debug(f"尝试连接到 SSE 端点: {sse_url}")
            
            # 测试 SSE 端点是否可访问
            async with self.session.get(sse_url) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'text/event-stream' in content_type:
                        # 对于 FastMCP，我们需要处理 SSE 流以获取会话端点
                        self.base_url = sse_url
                        
                        # 处理 SSE 流以获取会话端点
                        logger.debug("处理 SSE 流以获取会话端点...")
                        try:
                            endpoint_url = await self._extract_endpoint_from_sse(response)
                            if endpoint_url:
                                self.session_endpoint = endpoint_url
                                logger.info(f"获取到会话端点: {endpoint_url}")
                        except Exception as e:
                            logger.debug(f"提取会话端点失败: {e}")
                        
                        self.connected = True
                        logger.info(f"成功连接到 SSE 端点: {sse_url}")
                        return
                    elif 'application/json' in content_type:
                        # 对于 FastMCP，我们只需要确认 SSE 端点存在
                        self.base_url = sse_url
                        self.connected = True
                        logger.info(f"成功连接到 SSE 端点: {sse_url}")
                        return
                    else:
                        logger.debug(f"SSE 端点返回了非预期的内容类型: {content_type}")
                elif response.status == 307:
                    # 处理重定向
                    redirect_url = response.headers.get('location', '')
                    logger.debug(f"SSE 端点重定向到: {redirect_url}")
                    if redirect_url:
                        # 使用重定向后的 URL
                        self.base_url = redirect_url
                        self.connected = True
                        logger.info(f"通过重定向连接到 SSE 端点: {redirect_url}")
                        return
                else:
                    logger.debug(f"SSE 端点返回状态码: {response.status}")
                    
        except Exception as e:
            logger.debug(f"连接 SSE 端点失败 {sse_url}: {e}")
        
        # 如果直接连接失败，尝试构建其他可能的 SSE 端点
        possible_sse_endpoints = []
        
        # 从原始 URL 构建可能的 SSE 端点
        if not self.config.url.endswith('/sse'):
            possible_sse_endpoints.append(f"{self.config.url.rstrip('/')}/sse")
        
        # 从基础 URL 构建 SSE 端点
        base_url = f"{parsed.scheme}://{parsed.netloc}"
        possible_sse_endpoints.extend([
            f"{base_url}/sse",
            f"{base_url.rstrip('/')}{parsed.path.rstrip('/')}/sse"
        ])
        
        for endpoint in possible_sse_endpoints:
            try:
                logger.debug(f"尝试备用 SSE 端点: {endpoint}")
                
                async with self.session.get(endpoint) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'text/event-stream' in content_type or 'application/json' in content_type:
                            self.base_url = endpoint
                            self.connected = True
                            logger.info(f"通过备用端点连接到 SSE: {endpoint}")
                            return
                    elif response.status == 307:
                        redirect_url = response.headers.get('location', '')
                        if redirect_url:
                            self.base_url = redirect_url
                            self.connected = True
                            logger.info(f"通过备用端点重定向连接到 SSE: {redirect_url}")
                            return
                            
            except Exception as e:
                logger.debug(f"备用 SSE 端点失败 {endpoint}: {e}")
                continue
        
        # 如果所有尝试都失败，假设服务器存在并使用原始 URL
        logger.warning("无法验证 SSE 端点，假设服务器可用")
        self.base_url = self.config.url
        self.connected = True
    
    async def send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送 SSE 请求"""
        if not self.connected or not self.session:
            raise RuntimeError("连接未建立")
        
        request = {
            "jsonrpc": "2.0",
            "id": str(asyncio.get_event_loop().time()),
            "method": method,
            "params": params or {}
        }
        
        logger.debug(f"发送 SSE 请求: {method}")
        
        try:
            # 对于工具调用，使用 POST 请求到会话端点
            if method.startswith("tools/") and self.session_endpoint:
                # 构建 POST 请求的 URL
                parsed_base = urllib.parse.urlparse(self.base_url)
                post_url = f"{parsed_base.scheme}://{parsed_base.netloc}{self.session_endpoint}"
                
                logger.debug(f"使用 POST 请求到会话端点: {post_url}")
                
                # 为每个工具调用创建一个新的客户端会话
                async with aiohttp.ClientSession() as tool_session:
                    # 发送初始化请求
                    logger.debug("发送初始化请求...")
                    init_request = {
                        "jsonrpc": "2.0",
                        "id": str(asyncio.get_event_loop().time()) + "_init",
                        "method": "initialize",
                        "params": {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {
                                "sampling": {}
                            },
                            "clientInfo": {
                                "name": "mcp-python-client",
                                "version": "1.0.0"
                            }
                        }
                    }
                    
                    try:
                        async with tool_session.post(
                            post_url,
                            json=init_request,
                            headers={"Content-Type": "application/json"},
                            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                        ) as init_response:
                            logger.debug(f"初始化响应状态: {init_response.status}")
                            if init_response.status not in [200, 202]:
                                logger.warning(f"初始化失败: {init_response.status}")
                    except Exception as e:
                        logger.error(f"初始化请求失败: {e}")
                    
                    # 等待一小段时间让服务器处理初始化
                    await asyncio.sleep(0.2)
                    
                    # 发送实际工具调用请求
                    async with tool_session.post(
                        post_url,
                        json=request,
                        headers={"Content-Type": "application/json"},
                        timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                    ) as response:
                        logger.debug(f"POST 响应状态: {response.status}")
                        
                        if response.status in [200, 202]:
                            # 对于 202 状态，我们需要检查 SSE 端点获取响应
                            if response.status == 202:
                                logger.debug("收到 202 Accepted，检查 SSE 端点获取响应")
                                # 等待一小段时间让服务器处理
                                await asyncio.sleep(0.5)
                                
                                # 使用相同的 ID 检查 SSE 端点
                                query_params = {
                                    'method': method,
                                    'id': request['id']
                                }
                                query_string = urllib.parse.urlencode(query_params)
                                check_url = f"{self.base_url}?{query_string}"
                                
                                async with tool_session.get(
                                    check_url,
                                    headers={
                                        "Accept": "text/event-stream",
                                        "Cache-Control": "no-cache"
                                    },
                                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                                ) as check_response:
                                    if check_response.status == 200:
                                        result = await self._handle_sse_stream(check_response)
                                        
                                        # 检查是否收到了新的会话端点
                                        if (result.get("result") and 
                                            isinstance(result["result"], dict) and 
                                            "endpoint" in result["result"] and
                                            result["result"].get("message") == "FastMCP requires using the provided endpoint for actual requests"):
                                            
                                            # 更新会话端点
                                            new_endpoint = result["result"]["endpoint"]
                                            old_endpoint = self.session_endpoint
                                            self.session_endpoint = new_endpoint
                                            logger.info(f"更新会话端点: {old_endpoint} -> {new_endpoint}")
                                            
                                            # 重置重试计数
                                            self._retry_count = 0
                                            
                                            # 使用新的会话端点重试请求
                                            logger.debug("使用新的会话端点重试请求...")
                                            return await self.send_request(method, params)
                                        
                                        return result
                                    else:
                                        # 如果检查失败，返回原始 POST 响应
                                        try:
                                            return await response.json()
                                        except:
                                            text_response = await response.text()
                                            return {
                                                "jsonrpc": "2.0",
                                                "id": request['id'],
                                                "result": {"content": text_response}
                                            }
                            else:
                                # 对于 200 状态，直接解析响应
                                try:
                                    return await response.json()
                                except:
                                    text_response = await response.text()
                                    return {
                                        "jsonrpc": "2.0",
                                        "id": request['id'],
                                        "result": {"content": text_response}
                                    }
                        else:
                            error_text = await response.text()
                            logger.error(f"POST 请求失败: {response.status}, 响应: {error_text}")
                            raise RuntimeError(f"POST 请求失败: {response.status}")
            else:
                # 对于非工具调用或没有会话端点的情况，使用 GET 请求
                # 将请求参数编码到查询字符串中
                query_params = {
                    'method': method,
                    'id': request['id']
                }
                if params:
                    query_params['params'] = json.dumps(params)
                
                query_string = urllib.parse.urlencode(query_params)
                
                # 优先使用会话端点（如果可用）
                if self.session_endpoint:
                    # 对于会话端点，我们需要构建完整的 URL
                    parsed_base = urllib.parse.urlparse(self.base_url)
                    session_url = f"{parsed_base.scheme}://{parsed_base.netloc}{self.session_endpoint}?{query_string}"
                    logger.debug(f"使用会话端点: {session_url}")
                    sse_url = session_url
                else:
                    sse_url = f"{self.base_url}?{query_string}"
                
                logger.debug(f"SSE 请求 URL: {sse_url}")
                
                async with self.session.get(
                    sse_url,
                    headers={
                        "Accept": "text/event-stream",
                        "Cache-Control": "no-cache"
                    },
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                ) as response:
                    
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        logger.debug(f"响应 Content-Type: {content_type}")
                        
                        if 'text/event-stream' in content_type:
                            return await self._handle_sse_stream(response)
                        else:
                            # 某些 FastMCP 实现可能直接返回 JSON
                            try:
                                return await response.json()
                            except:
                                # 如果 JSON 解析失败，尝试作为文本处理
                                text_response = await response.text()
                                logger.debug(f"文本响应: {text_response}")
                                # 尝试从文本中提取 JSON
                                try:
                                    # 查找 JSON 对象
                                    json_start = text_response.find('{')
                                    json_end = text_response.rfind('}') + 1
                                    if json_start >= 0 and json_end > json_start:
                                        json_str = text_response[json_start:json_end]
                                        return json.loads(json_str)
                                except:
                                    pass
                                
                                # 如果无法解析 JSON，返回简单的成功响应
                                return {
                                    "jsonrpc": "2.0",
                                    "id": request['id'],
                                    "result": {"content": text_response}
                                }
                    else:
                        error_text = await response.text()
                        logger.error(f"SSE 请求失败: {response.status}, 响应: {error_text}")
                        raise RuntimeError(f"SSE 请求失败: {response.status}")
                    
        except Exception as e:
            logger.error(f"请求失败: {e}")
            raise
        
        # 如果所有尝试都失败
        raise RuntimeError(f"无法连接到 SSE 服务器")
    
    async def _handle_sse_stream(self, response) -> Dict[str, Any]:
        """处理 SSE 流响应"""
        logger.debug("开始处理 SSE 流")
        
        try:
            # FastMCP 使用特定的 SSE 格式，需要特殊处理
            endpoint_url = None
            session_id = None
            new_session_endpoint = None
            
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                logger.debug(f"SSE 行: {line_str}")
                
                if line_str.startswith('event: '):
                    event_type = line_str[7:]
                    logger.debug(f"SSE 事件类型: {event_type}")
                    
                elif line_str.startswith('data: '):
                    data = line_str[6:]  # 去掉 'data: ' 前缀
                    
                    # 跳过空数据
                    if not data.strip():
                        continue
                    
                    # FastMCP 可能返回端点信息
                    if event_type == 'endpoint':
                        endpoint_url = data
                        # 保存会话端点供后续请求使用
                        new_session_endpoint = endpoint_url
                        logger.debug(f"收到端点信息: {endpoint_url}")
                        continue
                    
                    # 尝试解析 JSON 数据
                    try:
                        result = json.loads(data)
                        logger.debug(f"解析的 SSE 数据: {result}")
                        
                        # 检查是否是有效的 JSON-RPC 响应
                        if isinstance(result, dict) and ('result' in result or 'error' in result):
                            # 如果收到了新的会话端点，更新它
                            if new_session_endpoint:
                                self.session_endpoint = new_session_endpoint
                                logger.info(f"更新会话端点: {new_session_endpoint}")
                            return result
                        
                        # 如果不是标准 JSON-RPC，但包含有用信息，构造响应
                        if isinstance(result, dict):
                            # 如果收到了新的会话端点，更新它
                            if new_session_endpoint:
                                self.session_endpoint = new_session_endpoint
                                logger.info(f"更新会话端点: {new_session_endpoint}")
                            return {
                                "jsonrpc": "2.0",
                                "id": str(asyncio.get_event_loop().time()),
                                "result": result
                            }
                        
                    except json.JSONDecodeError as e:
                        logger.debug(f"JSON 解析失败: {e}, 数据: {data}")
                        
                elif line_str.startswith('id: '):
                    event_id = line_str[4:]
                    logger.debug(f"SSE 事件ID: {event_id}")
                
                # 处理 ping 事件
                elif line_str.startswith(': ping'):
                    logger.debug("收到 ping 事件")
                    continue
            
            # 如果 FastMCP 返回了端点信息，我们需要使用该端点进行实际请求
            if endpoint_url:
                logger.info(f"FastMCP 返回了专用端点: {endpoint_url}")
                # 更新会话端点
                self.session_endpoint = endpoint_url
                # 这里我们可以构造一个模拟响应，表示需要使用新端点
                return {
                    "jsonrpc": "2.0",
                    "result": {
                        "endpoint": endpoint_url,
                        "message": "FastMCP requires using the provided endpoint for actual requests"
                    }
                }
            
            # 如果没有收到有效数据
            logger.warning("SSE 流结束但未收到有效响应")
            return {"jsonrpc": "2.0", "error": {"code": -32603, "message": "No valid response from SSE stream"}}
            
        except Exception as e:
            logger.error(f"处理 SSE 流时发生错误: {e}")
            raise