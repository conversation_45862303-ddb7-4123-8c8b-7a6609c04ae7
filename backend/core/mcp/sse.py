"""
MCP (Model Context Protocol) SSE 传输连接实现
"""
import asyncio
import json
import logging
import urllib.parse
from typing import Dict, Any, Optional

import aiohttp

from .base import MCPConnection

logger = logging.getLogger(__name__)

class MCPSSEConnection(MCPConnection):
    """SSE 传输连接"""
    
    def __init__(self, config):
        super().__init__(config)
        self.session: aiohttp.ClientSession = None
        self.base_url: str = None
        self.session_endpoint: str = None
        self._retry_count = 0
        self._initialized = False
        
    async def connect(self) -> bool:
        """连接到 SSE 端点"""
        try:
            self.session = aiohttp.ClientSession()
            
            # 检测端点
            await self._detect_endpoints()
            
            if self.connected:
                logger.info(f"SSE MCP 服务器连接成功: {self.config.url}")
                return True
            else:
                logger.error(f"SSE MCP 服务器连接失败: {self.config.url}")
                return False
                
        except Exception as e:
            logger.error(f"SSE 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开 SSE 连接"""
        if self.session:
            await self.session.close()
            self.session = None
        self.connected = False
        self.base_url = None
        self.session_endpoint = None
        self._retry_count = 0
        self._initialized = False
    
    async def _extract_endpoint_from_sse(self, response) -> Optional[str]:
        """从 SSE 流中提取会话端点"""
        try:
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                
                if line_str.startswith('event: endpoint'):
                    # 下一行应该是 data
                    try:
                        next_line = await response.content.readline()
                        if next_line:
                            next_line_str = next_line.decode('utf-8').strip()
                            if next_line_str.startswith('data: '):
                                endpoint_url = next_line_str[6:]  # 去掉 'data: ' 前缀
                                if endpoint_url.strip():
                                    return endpoint_url
                    except:
                        continue
                
                # 如果遇到空行，可能是事件结束
                elif not line_str:
                    continue
                    
                # 如果遇到 ping 事件，继续
                elif line_str.startswith(': ping'):
                    continue
                    
                # 如果遇到其他事件，继续
                elif line_str.startswith('event: '):
                    continue
                    
                # 如果遇到数据，但不是端点事件，继续
                elif line_str.startswith('data: '):
                    continue
        except Exception as e:
            logger.debug(f"提取会话端点时发生错误: {e}")
            
        return None
    
    async def _detect_endpoints(self):
        """智能检测 MCP 服务器端点"""
        # 解析 URL
        parsed = urllib.parse.urlparse(self.config.url)
        
        # 构建 SSE 端点 URL
        sse_url = self.config.url
        
        # FastMCP 服务器通常只提供 SSE 端点，没有健康检查端点
        # 直接尝试连接到 SSE 端点
        try:
            logger.debug(f"尝试连接到 SSE 端点: {sse_url}")
            
            # 测试 SSE 端点是否可访问
            async with self.session.get(sse_url) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'text/event-stream' in content_type:
                        # 对于 FastMCP，我们需要处理 SSE 流以获取会话端点
                        self.base_url = sse_url
                        
                        # 处理 SSE 流以获取会话端点
                        logger.debug("处理 SSE 流以获取会话端点...")
                        try:
                            endpoint_url = await self._extract_endpoint_from_sse(response)
                            if endpoint_url:
                                self.session_endpoint = endpoint_url
                                logger.info(f"获取到会话端点: {endpoint_url}")
                        except Exception as e:
                            logger.debug(f"提取会话端点失败: {e}")
                        
                        self.connected = True
                        logger.info(f"成功连接到 SSE 端点: {sse_url}")
                        return
                    elif 'application/json' in content_type:
                        # 对于 FastMCP，我们只需要确认 SSE 端点存在
                        self.base_url = sse_url
                        self.connected = True
                        logger.info(f"成功连接到 SSE 端点: {sse_url}")
                        return
                    else:
                        logger.debug(f"SSE 端点返回了非预期的内容类型: {content_type}")
                elif response.status == 307:
                    # 处理重定向
                    redirect_url = response.headers.get('location', '')
                    logger.debug(f"SSE 端点重定向到: {redirect_url}")
                    if redirect_url:
                        # 使用重定向后的 URL
                        self.base_url = redirect_url
                        self.connected = True
                        logger.info(f"通过重定向连接到 SSE 端点: {redirect_url}")
                        return
                else:
                    logger.debug(f"SSE 端点返回状态码: {response.status}")
                    
        except Exception as e:
            logger.debug(f"连接 SSE 端点失败 {sse_url}: {e}")
        
        # 如果直接连接失败，尝试构建其他可能的 SSE 端点
        possible_sse_endpoints = []
        
        # 从原始 URL 构建可能的 SSE 端点
        if not self.config.url.endswith('/sse'):
            possible_sse_endpoints.append(f"{self.config.url.rstrip('/')}/sse")
        
        # 从基础 URL 构建 SSE 端点
        base_url = f"{parsed.scheme}://{parsed.netloc}"
        possible_sse_endpoints.extend([
            f"{base_url}/sse",
            f"{base_url.rstrip('/')}{parsed.path.rstrip('/')}/sse"
        ])
        
        for endpoint in possible_sse_endpoints:
            try:
                logger.debug(f"尝试备用 SSE 端点: {endpoint}")
                
                async with self.session.get(endpoint) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'text/event-stream' in content_type or 'application/json' in content_type:
                            self.base_url = endpoint
                            self.connected = True
                            logger.info(f"通过备用端点连接到 SSE: {endpoint}")
                            return
                    elif response.status == 307:
                        redirect_url = response.headers.get('location', '')
                        if redirect_url:
                            self.base_url = redirect_url
                            self.connected = True
                            logger.info(f"通过备用端点重定向连接到 SSE: {redirect_url}")
                            return
                            
            except Exception as e:
                logger.debug(f"备用 SSE 端点失败 {endpoint}: {e}")
                continue
        
        # 如果所有尝试都失败，假设服务器存在并使用原始 URL
        logger.warning("无法验证 SSE 端点，假设服务器可用")
        self.base_url = self.config.url
        self.connected = True
    
    async def send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送 SSE 请求"""
        if not self.connected or not self.session:
            raise RuntimeError("连接未建立")

        request = {
            "jsonrpc": "2.0",
            "id": str(asyncio.get_event_loop().time()),
            "method": method,
            "params": params or {}
        }

        logger.debug(f"发送 SSE 请求: {method}")

        try:
            # 确保有会话端点
            if not self.session_endpoint:
                logger.error("会话端点未设置")
                raise RuntimeError("会话端点未设置")

            # 构建会话 URL
            parsed_base = urllib.parse.urlparse(self.base_url)
            session_url = f"{parsed_base.scheme}://{parsed_base.netloc}{self.session_endpoint}"

            logger.debug(f"使用会话端点: {session_url}")

            # 创建一个专用的 SSE 连接来处理这个请求
            return await self._send_request_with_sse(session_url, request)

        except Exception as e:
            logger.error(f"请求失败: {e}")
            raise

    async def _send_request_with_sse(self, session_url: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """使用 SSE 连接发送请求并等待响应"""

        # 创建一个新的会话来处理这个请求
        async with aiohttp.ClientSession() as request_session:
            # 1. 首先建立 SSE 连接来监听响应
            sse_task = None
            response_future = asyncio.Future()

            try:
                # 启动 SSE 监听任务
                sse_task = asyncio.create_task(
                    self._listen_for_response(request_session, session_url, request['id'], response_future)
                )

                # 等待一小段时间确保 SSE 连接建立
                await asyncio.sleep(0.1)

                # 2. 发送 POST 请求
                logger.debug(f"发送 POST 请求: {request}")
                async with request_session.post(
                    session_url,
                    json=request,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as post_response:
                    logger.debug(f"POST 响应状态: {post_response.status}")

                    if post_response.status == 200:
                        # 直接返回响应
                        try:
                            result = await post_response.json()
                            if not response_future.done():
                                response_future.set_result(result)
                        except Exception as e:
                            logger.error(f"解析 POST 响应失败: {e}")
                            text_response = await post_response.text()
                            result = {
                                "jsonrpc": "2.0",
                                "id": request['id'],
                                "result": {"content": text_response}
                            }
                            if not response_future.done():
                                response_future.set_result(result)

                    elif post_response.status == 202:
                        # 等待 SSE 响应
                        logger.debug("等待 SSE 响应...")
                    else:
                        error_text = await post_response.text()
                        logger.error(f"POST 请求失败: {post_response.status}, 响应: {error_text}")
                        error_result = {
                            "jsonrpc": "2.0",
                            "id": request['id'],
                            "error": {
                                "code": -32603,
                                "message": f"POST request failed: {post_response.status}"
                            }
                        }
                        if not response_future.done():
                            response_future.set_result(error_result)

                # 3. 等待响应（来自 POST 或 SSE）
                try:
                    result = await asyncio.wait_for(response_future, timeout=self.config.timeout)
                    return result
                except asyncio.TimeoutError:
                    logger.error(f"等待响应超时 ({self.config.timeout}秒)")
                    return {
                        "jsonrpc": "2.0",
                        "id": request['id'],
                        "error": {
                            "code": -32603,
                            "message": f"Request timeout after {self.config.timeout} seconds"
                        }
                    }

            finally:
                # 清理 SSE 任务
                if sse_task and not sse_task.done():
                    sse_task.cancel()
                    try:
                        await sse_task
                    except asyncio.CancelledError:
                        pass

    async def _listen_for_response(self, session: aiohttp.ClientSession, session_url: str, request_id: str, response_future: asyncio.Future):
        """监听 SSE 响应"""
        try:
            logger.debug(f"开始监听 SSE 响应，请求ID: {request_id}")

            # 构建 SSE URL，包含必要的查询参数
            sse_url = f"{session_url}?_stream=true"
            logger.debug(f"SSE 监听 URL: {sse_url}")

            async with session.get(
                sse_url,
                headers={
                    "Accept": "text/event-stream",
                    "Cache-Control": "no-cache"
                },
                timeout=aiohttp.ClientTimeout(total=self.config.timeout + 5)
            ) as sse_response:
                if sse_response.status != 200:
                    logger.error(f"SSE 连接失败: {sse_response.status}")
                    # 尝试读取错误信息
                    try:
                        error_text = await sse_response.text()
                        logger.error(f"SSE 错误详情: {error_text}")
                    except:
                        pass

                    if not response_future.done():
                        response_future.set_result({
                            "jsonrpc": "2.0",
                            "id": request_id,
                            "error": {
                                "code": -32603,
                                "message": f"SSE connection failed: {sse_response.status}"
                            }
                        })
                    return

                logger.debug("SSE 连接建立成功，开始监听...")

                # 设置一个超时计时器
                start_time = asyncio.get_event_loop().time()
                max_wait_time = self.config.timeout

                async for line in sse_response.content:
                    if response_future.done():
                        break

                    # 检查超时
                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > max_wait_time:
                        logger.warning(f"SSE 监听超时 ({max_wait_time}秒)")
                        if not response_future.done():
                            response_future.set_result({
                                "jsonrpc": "2.0",
                                "id": request_id,
                                "error": {
                                    "code": -32603,
                                    "message": f"SSE listening timeout after {max_wait_time} seconds"
                                }
                            })
                        break

                    line_str = line.decode('utf-8').strip()
                    if not line_str:
                        continue

                    logger.debug(f"SSE 行: {line_str}")

                    # 处理数据行
                    if line_str.startswith('data: '):
                        data = line_str[6:]  # 去掉 'data: ' 前缀

                        if not data.strip():
                            continue

                        try:
                            result = json.loads(data)
                            logger.debug(f"解析的 SSE 数据: {result}")

                            # 检查是否是我们等待的响应
                            if isinstance(result, dict) and result.get('id') == request_id:
                                logger.debug(f"收到匹配的响应: {result}")
                                if not response_future.done():
                                    response_future.set_result(result)
                                break

                            # 检查是否是有效的 JSON-RPC 响应（即使 ID 不匹配）
                            elif isinstance(result, dict) and ('result' in result or 'error' in result):
                                logger.debug(f"收到 JSON-RPC 响应: {result}")
                                if not response_future.done():
                                    response_future.set_result(result)
                                break

                        except json.JSONDecodeError as e:
                            logger.debug(f"JSON 解析失败: {e}, 数据: {data}")

                    # 处理 ping 事件
                    elif line_str.startswith(': ping'):
                        logger.debug("收到 ping 事件")
                        continue

                    # 处理事件类型
                    elif line_str.startswith('event: '):
                        event_type = line_str[7:]
                        logger.debug(f"SSE 事件类型: {event_type}")
                        continue

        except asyncio.CancelledError:
            logger.debug("SSE 监听任务被取消")
            raise
        except Exception as e:
            logger.error(f"SSE 监听失败: {e}")
            if not response_future.done():
                response_future.set_result({
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32603,
                        "message": f"SSE listening failed: {str(e)}"
                    }
                })
    
    async def _handle_sse_stream(self, response) -> Dict[str, Any]:
        """处理 SSE 流响应"""
        logger.debug("开始处理 SSE 流")

        try:
            # FastMCP 使用特定的 SSE 格式，需要特殊处理
            endpoint_url = None
            new_session_endpoint = None
            event_type = None

            # 添加超时保护，避免无限等待
            timeout_seconds = min(self.config.timeout, 30)  # 最多等待30秒
            start_time = asyncio.get_event_loop().time()

            async for line in response.content:
                # 检查是否超时
                current_time = asyncio.get_event_loop().time()
                if current_time - start_time > timeout_seconds:
                    logger.warning(f"SSE 流处理超时 ({timeout_seconds}秒)")
                    break

                line_str = line.decode('utf-8').strip()
                logger.debug(f"SSE 行: {line_str}")

                if line_str.startswith('event: '):
                    event_type = line_str[7:]
                    logger.debug(f"SSE 事件类型: {event_type}")

                elif line_str.startswith('data: '):
                    data = line_str[6:]  # 去掉 'data: ' 前缀

                    # 跳过空数据
                    if not data.strip():
                        continue

                    # FastMCP 可能返回端点信息
                    if event_type == 'endpoint':
                        endpoint_url = data
                        # 保存会话端点供后续请求使用
                        new_session_endpoint = endpoint_url
                        logger.debug(f"收到端点信息: {endpoint_url}")
                        continue

                    # 尝试解析 JSON 数据
                    try:
                        result = json.loads(data)
                        logger.debug(f"解析的 SSE 数据: {result}")

                        # 检查是否是有效的 JSON-RPC 响应
                        if isinstance(result, dict) and ('result' in result or 'error' in result):
                            # 如果收到了新的会话端点，更新它
                            if new_session_endpoint:
                                self.session_endpoint = new_session_endpoint
                                logger.info(f"更新会话端点: {new_session_endpoint}")
                            return result

                        # 如果不是标准 JSON-RPC，但包含有用信息，构造响应
                        if isinstance(result, dict):
                            # 如果收到了新的会话端点，更新它
                            if new_session_endpoint:
                                self.session_endpoint = new_session_endpoint
                                logger.info(f"更新会话端点: {new_session_endpoint}")
                            return {
                                "jsonrpc": "2.0",
                                "id": str(asyncio.get_event_loop().time()),
                                "result": result
                            }

                    except json.JSONDecodeError as e:
                        logger.debug(f"JSON 解析失败: {e}, 数据: {data}")

                elif line_str.startswith('id: '):
                    event_id = line_str[4:]
                    logger.debug(f"SSE 事件ID: {event_id}")

                # 处理 ping 事件
                elif line_str.startswith(': ping'):
                    logger.debug("收到 ping 事件")
                    continue

                # 处理空行（SSE 事件分隔符）
                elif not line_str:
                    # 重置事件类型
                    event_type = None
                    continue

            # 如果 FastMCP 返回了端点信息，我们需要使用该端点进行实际请求
            if endpoint_url:
                logger.info(f"FastMCP 返回了专用端点: {endpoint_url}")
                # 更新会话端点
                self.session_endpoint = endpoint_url
                # 这里我们可以构造一个模拟响应，表示需要使用新端点
                return {
                    "jsonrpc": "2.0",
                    "result": {
                        "endpoint": endpoint_url,
                        "message": "FastMCP requires using the provided endpoint for actual requests"
                    }
                }

            # 如果没有收到有效数据
            logger.warning("SSE 流结束但未收到有效响应")
            return {"jsonrpc": "2.0", "error": {"code": -32603, "message": "No valid response from SSE stream"}}

        except asyncio.TimeoutError:
            logger.error("SSE 流处理超时")
            return {"jsonrpc": "2.0", "error": {"code": -32603, "message": "SSE stream timeout"}}
        except Exception as e:
            logger.error(f"处理 SSE 流时发生错误: {e}")
            raise