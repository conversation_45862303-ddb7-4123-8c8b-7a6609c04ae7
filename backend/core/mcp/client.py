"""
MCP (Model Context Protocol) 客户端实现
"""
import asyncio
import json
import logging
from dataclasses import asdict
from typing import Dict, List, Any

from .config import MCPTransportType, MCPServerConfig, MCPTool, MCPServerStatus
from .base import MCPConnection
from .stdio import MCPStdioConnection
from .sse import MCPSSEConnection
from .websocket import MCPWebSocketConnection

logger = logging.getLogger(__name__)

class MCPClient:
    """MCP 客户端"""
    
    def __init__(self):
        self.servers: Dict[str, MCPServerConfig] = {}
        self.connections: Dict[str, MCPConnection] = {}
        self.tools: Dict[str, MCPTool] = {}
        self.server_status: Dict[str, MCPServerStatus] = {}
    
    def add_server(self, config: MCPServerConfig):
        """添加 MCP 服务器配置"""
        self.servers[config.name] = config
        self.server_status[config.name] = MCPServerStatus(
            name=config.name,
            status="disconnected",
            transport_type=config.transport_type
        )
    
    def _create_connection(self, config: MCPServerConfig) -> MCPConnection:
        """创建连接"""
        if config.transport_type == MCPTransportType.STDIO:
            return MCPStdioConnection(config)
        elif config.transport_type == MCPTransportType.SSE:
            return MCPSSEConnection(config)
        elif config.transport_type == MCPTransportType.STREAMABLE_HTTP:
            return MCPSSEConnection(config)  # 暂时使用 SSE 连接
        else:
            raise ValueError(f"不支持的传输类型: {config.transport_type}")
    
    async def start_server(self, server_name: str) -> bool:
        """启动 MCP 服务器"""
        if server_name not in self.servers:
            logger.error(f"未找到服务器配置: {server_name}")
            return False
        
        server_config = self.servers[server_name]
        
        try:
            # 停止已存在的连接
            await self.stop_server(server_name)
            
            # 创建连接
            connection = self._create_connection(server_config)
            
            # 尝试连接
            success = await connection.connect()
            
            if success:
                self.connections[server_name] = connection
                
                # 获取连接信息
                connection_info = ""
                if server_config.transport_type == MCPTransportType.STDIO:
                    if hasattr(connection, 'process') and connection.process:
                        connection_info = f"PID: {connection.process.pid}"
                else:
                    connection_info = server_config.url
                
                self.server_status[server_name] = MCPServerStatus(
                    name=server_name,
                    status="connected",
                    transport_type=server_config.transport_type,
                    connection_info=connection_info,
                    pid=getattr(connection.process, 'pid', None) if hasattr(connection, 'process') else None
                )
                
                # 尝试获取工具列表
                await self._discover_tools(server_name)
                
                logger.info(f"MCP 服务器启动成功: {server_name} ({server_config.transport_type.value})")
                return True
            else:
                error_msg = "连接失败"
                self.server_status[server_name] = MCPServerStatus(
                    name=server_name,
                    status="error",
                    transport_type=server_config.transport_type,
                    error_message=error_msg
                )
                logger.error(f"MCP 服务器启动失败: {server_name}, {error_msg}")
                return False
                
        except Exception as e:
            error_msg = f"启动服务器时发生异常: {str(e)}"
            self.server_status[server_name] = MCPServerStatus(
                name=server_name,
                status="error",
                transport_type=server_config.transport_type,
                error_message=error_msg
            )
            logger.error(f"启动 MCP 服务器失败: {server_name}, {error_msg}")
            return False
    
    async def stop_server(self, server_name: str) -> bool:
        """停止 MCP 服务器"""
        if server_name in self.connections:
            try:
                connection = self.connections[server_name]
                await connection.disconnect()
                del self.connections[server_name]
                logger.info(f"MCP 服务器已停止: {server_name}")
                
            except Exception as e:
                logger.error(f"停止 MCP 服务器失败: {server_name}, {e}")
                return False
        
        # 更新状态
        if server_name in self.server_status:
            self.server_status[server_name].status = "disconnected"
            self.server_status[server_name].pid = None
            self.server_status[server_name].connection_info = None
            
        # 清理工具
        tools_to_remove = [name for name, tool in self.tools.items() 
                          if tool.server_name == server_name]
        for tool_name in tools_to_remove:
            del self.tools[tool_name]
            
        return True
    
    async def _discover_tools(self, server_name: str):
        """发现服务器提供的工具"""
        if server_name not in self.connections:
            return
            
        try:
            connection = self.connections[server_name]
            
            # 对于 FastMCP 服务器，我们直接使用预定义的工具列表
            # 因为 FastMCP 的工具发现机制与我们当前的实现不兼容
            tools = []
            
            if server_name == "city":
                # 城市分级查询服务器的预定义工具
                predefined_tools = [
                    {
                        "name": "get_city_tier",
                        "description": "根据城市名称查询城市属于哪一线城市",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "city_name": {
                                    "type": "string",
                                    "description": "要查询的城市名称"
                                }
                            },
                            "required": ["city_name"]
                        }
                    },
                    {
                        "name": "list_cities_by_tier",
                        "description": "列出指定分级的所有城市",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "tier": {
                                    "type": "string",
                                    "description": "城市分级（一线城市、新一线城市、二线城市、三线城市、四线城市、五线城市）"
                                }
                            },
                            "required": ["tier"]
                        }
                    },
                    {
                        "name": "get_all_tiers",
                        "description": "获取所有城市分级的统计信息",
                        "inputSchema": {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    },
                    {
                        "name": "search_cities",
                        "description": "根据关键词搜索城市",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "keyword": {
                                    "type": "string",
                                    "description": "搜索关键词"
                                }
                            },
                            "required": ["keyword"]
                        }
                    }
                ]
                
                for tool_data in predefined_tools:
                    tool = MCPTool(
                        name=tool_data["name"],
                        description=tool_data["description"],
                        input_schema=tool_data["inputSchema"],
                        server_name=server_name
                    )
                    tools.append(tool)
                    self.tools[tool.name] = tool
                    
            else:
                # 对于其他服务器，尝试标准工具发现
                try:
                    response = await connection.send_request("tools/list")
                    logger.debug(f"工具列表响应: {response}")
                    
                    if response.get("result") and "tools" in response["result"]:
                        # 标准 MCP 响应格式
                        tools_data = response["result"]["tools"]
                        for tool_data in tools_data:
                            tool = MCPTool(
                                name=tool_data["name"],
                                description=tool_data.get("description", ""),
                                input_schema=tool_data.get("inputSchema", {}),
                                server_name=server_name
                            )
                            tools.append(tool)
                            self.tools[tool.name] = tool
                    else:
                        # 如果标准工具发现失败，创建一个通用工具
                        tool = MCPTool(
                            name=f"{server_name}_query",
                            description=f"查询 {server_name} 服务器",
                            input_schema={
                                "type": "object",
                                "properties": {
                                    "query": {"type": "string", "description": "查询内容"}
                                },
                                "required": ["query"]
                            },
                            server_name=server_name
                        )
                        tools.append(tool)
                        self.tools[tool.name] = tool
                        
                except Exception as e:
                    logger.error(f"标准工具发现失败 {server_name}: {e}")
                    # 如果标准工具发现失败，创建一个通用工具
                    tool = MCPTool(
                        name=f"{server_name}_query",
                        description=f"查询 {server_name} 服务器",
                        input_schema={
                            "type": "object",
                            "properties": {
                                "query": {"type": "string", "description": "查询内容"}
                            },
                            "required": ["query"]
                        },
                        server_name=server_name
                    )
                    tools.append(tool)
                    self.tools[tool.name] = tool
                
            # 更新服务器状态
            self.server_status[server_name].tools = tools
            logger.info(f"发现 {len(tools)} 个工具来自服务器: {server_name}")
            
        except Exception as e:
            logger.error(f"发现工具失败 {server_name}: {e}")
            # 如果工具发现失败，添加一些模拟工具用于测试
            mock_tools = [
                MCPTool(
                    name=f"{server_name}_example_tool",
                    description=f"来自 {server_name} 的示例工具",
                    input_schema={
                        "type": "object",
                        "properties": {
                            "input": {"type": "string", "description": "输入参数"}
                        },
                        "required": ["input"]
                    },
                    server_name=server_name
                )
            ]
            
            self.server_status[server_name].tools = mock_tools
            for tool in mock_tools:
                self.tools[tool.name] = tool
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用 MCP 工具"""
        if tool_name not in self.tools:
            raise ValueError(f"未找到工具: {tool_name}")
            
        tool = self.tools[tool_name]
        server_name = tool.server_name
        
        if server_name not in self.connections:
            raise RuntimeError(f"服务器未连接: {server_name}")
        
        try:
            connection = self.connections[server_name]
            
            # 发送工具调用请求
            response = await connection.send_request("tools/call", {
                "name": tool_name,
                "arguments": parameters
            })
            
            logger.debug(f"工具调用响应: {response}")
            
            # 处理不同类型的响应
            if "result" in response:
                result = response["result"]
                
                # 如果结果是字符串，可能是 FastMCP 的直接响应
                if isinstance(result, str):
                    return {
                        "success": True,
                        "result": {"content": result},
                        "tool_name": tool_name
                    }
                
                # 如果结果是字典，直接使用
                elif isinstance(result, dict):
                    return {
                        "success": True,
                        "result": result,
                        "tool_name": tool_name
                    }
                
                # 其他类型，转换为字符串
                else:
                    return {
                        "success": True,
                        "result": {"content": str(result)},
                        "tool_name": tool_name
                    }
                    
            elif "error" in response:
                logger.error(f"工具调用失败: {tool_name}, {response['error']}")
                return {
                    "success": False,
                    "error": response["error"],
                    "tool_name": tool_name
                }
            else:
                # 如果响应没有明确的结果或错误，但整个响应可能包含有用信息
                if isinstance(response, dict):
                    return {
                        "success": True,
                        "result": response,
                        "tool_name": tool_name
                    }
                else:
                    return {
                        "success": True,
                        "result": {"content": str(response)},
                        "tool_name": tool_name
                    }
                
        except Exception as e:
            logger.error(f"调用工具失败: {tool_name}, {e}")
            raise
    
    async def start_all_servers(self):
        """启动所有未禁用的服务器"""
        tasks = []
        for server_name in self.servers:
            if not self.servers[server_name].disabled:
                tasks.append(self.start_server(server_name))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if r is True)
            logger.info(f"启动了 {success_count}/{len(tasks)} 个 MCP 服务器")
    
    async def stop_all_servers(self):
        """停止所有服务器"""
        tasks = []
        for server_name in list(self.connections.keys()):
            tasks.append(self.stop_server(server_name))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            logger.info("所有 MCP 服务器已停止")
    
    def get_server_status(self, server_name: str = None) -> Dict[str, Any]:
        """获取服务器状态"""
        if server_name:
            if server_name in self.server_status:
                return asdict(self.server_status[server_name])
            else:
                return {"name": server_name, "status": "not_configured"}
        else:
            return {name: asdict(status) for name, status in self.server_status.items()}
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取所有可用工具"""
        return [asdict(tool) for tool in self.tools.values()]

# 全局 MCP 客户端实例
mcp_client = MCPClient()