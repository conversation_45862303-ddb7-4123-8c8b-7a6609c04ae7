"""
单据审核器 - 根据单据类型和相关规章制度进行单据审核
"""

import logging
import json
from typing import Dict, Any, Optional, List

from core.logging_config import log_ai_flow, log_audit_flow

logger = logging.getLogger(__name__)

class DocumentAuditor:
    """单据审核器"""
    
    def __init__(self, llm_manager):
        """初始化单据审核器"""
        logger.info("[AUDIT] 初始化单据审核器")
        self.llm_manager = llm_manager
    
    async def audit_document(self, document_type: str, document_content: str, regulations: List[Dict]) -> Dict[str, Any]:
        """审核单据"""
        logger.info(f"[AUDIT] 开始审核单据，类型: {document_type}")
        log_audit_flow("审核单据", document_type=document_type, document_content=document_content)
        
        try:
            if not self.llm_manager.is_configured():
                logger.error("[AUDIT] LLM未配置")
                return {"success": False, "error": "LLM未配置"}
            
            # 格式化规章制度
            logger.debug("[AUDIT] 格式化规章制度")
            formatted_regulations = self._format_regulations(regulations)
            logger.debug(f"[AUDIT] 格式化后的规章制度长度: {len(formatted_regulations)} 字符")
            
            # 构建审核提示
            logger.debug("[AUDIT] 构建审核提示")
            audit_prompt = self._build_audit_prompt(document_type, document_content, formatted_regulations)
            logger.debug(f"[AUDIT] 审核提示长度: {len(audit_prompt)} 字符")
            
            # 调用LLM进行审核
            logger.info("[AUDIT] 调用LLM进行审核")
            log_ai_flow("LLM审核", input_data={"document_type": document_type, "prompt_length": len(audit_prompt)})
            response = await self.llm_manager.generate(audit_prompt)
            logger.debug(f"[AUDIT] LLM响应长度: {len(response)} 字符")
            log_ai_flow("LLM审核", output_data={"response_length": len(response)})
            
            # 解析响应
            logger.debug("[AUDIT] 解析LLM响应")
            try:
                # 尝试从响应中提取JSON
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                
                if json_start != -1 and json_end != -1:
                    json_str = response[json_start:json_end]
                    logger.debug(f"[AUDIT] 提取的JSON字符串长度: {len(json_str)} 字符")
                    result = json.loads(json_str)
                    logger.info("[AUDIT] 成功解析审核结果")
                    log_audit_flow("审核单据", audit_result=result)
                    
                    return {
                        "success": True,
                        "data": result
                    }
                else:
                    logger.error(f"[AUDIT] 无法从响应中提取JSON: {response}")
                    return {"success": False, "error": "无法从响应中提取JSON"}
                    
            except json.JSONDecodeError as e:
                logger.error(f"[AUDIT] 解析JSON失败: {str(e)}, 响应: {response}")
                return {"success": False, "error": f"解析JSON失败: {str(e)}"}
                
        except Exception as e:
            logger.error(f"[AUDIT] 审核单据失败: {str(e)}", exc_info=True)
            log_audit_flow("审核单据", error=str(e))
            return {"success": False, "error": str(e)}
    
    def _format_regulations(self, regulations: List[Dict]) -> str:
        """格式化规章制度"""
        logger.debug(f"[AUDIT] 开始格式化 {len(regulations)} 条规章制度")
        formatted_parts = []
        
        for i, regulation in enumerate(regulations):
            logger.debug(f"[AUDIT] 格式化第 {i+1}/{len(regulations)} 条规章制度")
            metadata = regulation.get("metadata", {})
            content = regulation.get("content", "")
            
            category = metadata.get("category", "")
            title = metadata.get("title", "")
            
            if category and title:
                formatted_part = f"### {category} - {title}\n\n{content}"
            elif category:
                formatted_part = f"### {category}\n\n{content}"
            else:
                formatted_part = content
            
            formatted_parts.append(formatted_part)
            logger.debug(f"[AUDIT] 第 {i+1} 条规章制度格式化完成，长度: {len(formatted_part)} 字符")
        
        result = "\n\n---\n\n".join(formatted_parts)
        logger.debug(f"[AUDIT] 所有规章制度格式化完成，总长度: {len(result)} 字符")
        return result
    
    def _build_audit_prompt(self, document_type: str, document_content: str, regulations: str) -> str:
        """构建单据审核提示"""
        logger.debug("[AUDIT] 构建单据审核提示")
        
        # 读取prompt模板文件
        try:
            with open("backend/core/agent/prompt_templates/audit_by_type.txt", "r", encoding="utf-8") as f:
                prompt_template = f.read()
            
            # 使用模板替换参数
            result = prompt_template.format(
                document_type=document_type,
                document_content=document_content,
                regulations=regulations
            )
            logger.debug("[AUDIT] 成功构建单据审核提示")
            return result
        except Exception as e:
            logger.error(f"[AUDIT] 读取单据审核prompt模板失败: {str(e)}")
            # 如果读取模板失败，使用硬编码的prompt作为后备
            return f"""
你是一个专业的财务单据审核专家，擅长根据单据类型和相关规章制度进行单据审核。

请根据以下信息，对单据进行审核：

单据类型：{document_type}
单据内容：
---
{document_content}
---

相关规章制度：
---
{regulations}
---

请按照以下JSON格式返回审核结果：
```json
{{
  "audit_result": "通过",
  "confidence": 0.9,
  "summary": "该差旅费报销单符合公司差旅费报销规定，所有费用均在标准范围内，票据齐全，审核通过。",
  "compliance_check": [
    {{
      "rule": "交通标准：飞机经济舱、高铁二等座、普通列车硬卧",
      "is_compliant": true,
      "details": "实际乘坐高铁二等座，符合规定"
    }},
    {{
      "rule": "住宿标准：一线城市不超过600元/天",
      "is_compliant": true,
      "details": "实际住宿费用500元/天，符合规定"
    }},
    {{
      "rule": "伙食补贴：100元/天",
      "is_compliant": true,
      "details": "实际伙食补贴100元/天，符合规定"
    }}
  ],
  "violations": [],
  "suggestions": [
    "建议在后续出差中提前预订住宿，以获得更优惠的价格"
  ],
  "required_documents": [
    "出差申请表",
    "交通票据",
    "住宿发票",
    "餐饮发票"
  ],
  "document_status": {{
    "出差申请表": "已提供",
    "交通票据": "已提供",
    "住宿发票": "已提供",
    "餐饮发票": "已提供"
  }}
}}
```

要求：
1. 仔细阅读单据内容和相关规章制度
2. 根据单据类型，检查单据是否符合相关规定
3. 对每项规定进行合规性检查，并提供详细说明
4. 如果发现违规行为，请在violations数组中详细说明
5. 提供改进建议，帮助用户更好地遵守规定
6. 列出该类型单据所需的全部文档
7. 检查已提供文档的完整性
8. 根据检查结果，给出审核结论（通过、不通过、需补充材料）
9. 为审核结果提供置信度（0-1之间的数值）
10. 确保返回的是有效的JSON格式
11. 不要在JSON格式前后添加任何其他文本或解释
"""
    
    async def full_audit_process(self, document_content: str) -> Dict[str, Any]:
        """执行完整的审核流程"""
        logger.info("[AUDIT] 开始执行完整审核流程")
        log_audit_flow("完整审核流程", document_content=document_content)
        
        try:
            # 步骤1：识别单据类型
            logger.info("[AUDIT] 步骤1：识别单据类型")
            from .document_type_identifier import get_document_type_identifier
            identifier = get_document_type_identifier(self.llm_manager)
            
            identify_result = await identifier.identify_document_type(document_content)
            if not identify_result["success"]:
                logger.error(f"[AUDIT] 单据类型识别失败: {identify_result['error']}")
                return identify_result
            
            identification_data = identify_result["data"]
            document_type = identification_data.get("document_type", "")
            confidence = identification_data.get("confidence", 0)
            logger.info(f"[AUDIT] 单据类型识别完成: {document_type} (置信度: {confidence:.2f})")
            
            regulation_categories = identification_data.get("relevant_regulation_categories", [])
            logger.debug(f"[AUDIT] 相关规章制度分类: {regulation_categories}")
            
            # 步骤2：获取相关规章制度
            logger.info("[AUDIT] 步骤2：获取相关规章制度")
            regulations_result = await identifier.get_relevant_regulations(document_type, regulation_categories)
            if not regulations_result["success"]:
                logger.error(f"[AUDIT] 获取相关规章制度失败: {regulations_result['error']}")
                return regulations_result
            
            regulations_data = regulations_result["data"]
            relevant_regulations = regulations_data.get("relevant_regulations", [])
            logger.info(f"[AUDIT] 获取到 {len(relevant_regulations)} 条相关规章制度")
            
            # 步骤3：执行审核
            logger.info("[AUDIT] 步骤3：执行审核")
            audit_result = await self.audit_document(document_type, document_content, relevant_regulations)
            if not audit_result["success"]:
                logger.error(f"[AUDIT] 审核失败: {audit_result['error']}")
                return audit_result
            
            logger.info("[AUDIT] 完整审核流程执行成功")
            
            # 组合完整结果
            result = {
                "success": True,
                "data": {
                    "identification": identification_data,
                    "regulations": regulations_data,
                    "audit": audit_result["data"]
                }
            }
            
            log_audit_flow("完整审核流程", audit_result=result)
            return result
            
        except Exception as e:
            logger.error(f"[AUDIT] 执行完整审核流程失败: {str(e)}", exc_info=True)
            log_audit_flow("完整审核流程", error=str(e))
            return {"success": False, "error": str(e)}


# 全局单据审核器实例
_document_auditor = None

def get_document_auditor(llm_manager) -> DocumentAuditor:
    """获取全局单据审核器实例"""
    global _document_auditor
    if _document_auditor is None:
        logger.info("[AUDIT] 创建全局单据审核器实例")
        _document_auditor = DocumentAuditor(llm_manager)
    return _document_auditor