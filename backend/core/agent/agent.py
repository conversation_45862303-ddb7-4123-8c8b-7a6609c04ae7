"""
智能体执行器 - 协调各个组件，执行智能体任务
"""

import logging
from typing import Dict, Any, List, Optional, Callable, AsyncGenerator
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
import json
import asyncio
from datetime import datetime
import uuid

from .llm import get_llm_manager
from .memory import get_memory
from .tools import get_tools, get_tool_registry
from .prompts import get_prompt_manager
from .chains import get_chain_manager
from .monitoring import monitor_performance, get_cache_manager

from core.db import AsyncSessionLocal, engine
from models.subject import SubjectAccount
from models.asset import Asset
from models.role_staff import Staff
from models.company import Company
from sqlalchemy.future import select

# 其余代码已全部通过数据库异步获取，无需全局变量

logger = logging.getLogger(__name__)

class AgentExecutor:
    """智能体执行器"""
    
    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or str(uuid.uuid4())
        self.llm_manager = get_llm_manager()
        self.memory = get_memory(self.session_id)
        self.prompt_manager = get_prompt_manager()
        self.chain_manager = get_chain_manager()
        self.tool_registry = get_tool_registry()
        self.cache_manager = get_cache_manager()

        # 设置链式调用的LLM
        if self.llm_manager.is_configured():
            llm = self.llm_manager.get_llm()
            if llm is not None:
                self.chain_manager.set_llm(llm)
    
    def configure_llm(self, api_key: str, base_url: str, model: str, **kwargs) -> bool:
        """配置语言模型"""
        logger.info(f"Entering configure_llm with api_key={api_key[:5]}..., base_url={base_url}, model={model}")
        try:
            self.llm_manager.configure(api_key, base_url, model, **kwargs)
            llm = self.llm_manager.get_llm()
            if llm is not None:
                self.chain_manager.set_llm(llm)
            return True
        except Exception as e:
            logger.error(f"配置LLM失败: {str(e)}")
            return False
    
    @monitor_performance("process_message")
    async def process_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """处理用户消息"""
        logger.info
        if not self.llm_manager.is_configured():
            return {"success": False, "error": "LLM未配置"}
        try:
            # 处理文件内容
            file_content = ""
            if files and len(files) > 0:
                file_content = await self._process_files(files)
                # 将文件内容添加到用户消息中
                enhanced_message = f"{message}\n\n附件内容：\n{file_content}" if message.strip() else f"请分析以下文件内容：\n{file_content}"
            else:
                enhanced_message = message
            
            self.memory.add_user_message(enhanced_message)
            
            # 收集上下文数据
            context_data = await self._gather_context_data(user_id)
            
            # subjects 压缩为 {'借': [...], '贷': [...]} 格式
            subjects_raw = context_data.get("subjects", [])
            subjects_compact = [subj.get('科目名称') or subj.get('name') for subj in subjects_raw]
            
            # Process the entire chain with retry mechanism
            voucher_result = await self._process_chain_with_retry(
                enhanced_message, context_data, user_id
            )
            
            if not voucher_result["success"]:
                return {"success": False, "error": voucher_result["error"]}
            
            data = voucher_result["data"]
            
            self.memory.add_ai_message(json.dumps(data, ensure_ascii=False))
            return {
                "success": True,
                "data": data,
                "session_id": self.session_id
            }
        except asyncio.CancelledError:
            logger.warning("Process message was cancelled by client")
            return {"success": False, "error": "请求被用户取消"}
        except Exception as e:
            logger.error(f"处理消息失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    @monitor_performance("stream_message")
    async def stream_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理用户消息"""
        logger.info(f"Entering stream_message with message={message}, user_id={user_id}")
        if not self.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        try:
            # 处理文件内容
            file_content = ""
            if files and len(files) > 0:
                file_content = await self._process_files(files)
                # 将文件内容添加到用户消息中
                enhanced_message = f"{message}\n\n附件内容：\n{file_content}" if message.strip() else f"请分析以下文件内容：\n{file_content}"
            else:
                enhanced_message = message

            logger.info(f"enhanced_message : {enhanced_message}")
            
            self.memory.add_user_message(enhanced_message)
            
            # 收集上下文数据
            context_data = await self._gather_context_data(user_id)
            
            # subjects 压缩为 {'借': [...], '贷': [...]} 格式
            subjects_raw = context_data.get("subjects", [])
            subjects_compact = [subj.get('科目名称') or subj.get('name') for subj in subjects_raw]
            
            # Step 1: Analyze the transaction
            context_company = context_data.get("company", {})
            transaction_inputs = {
                "user_input": enhanced_message,
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "company_name": context_company.get("name", ""),
                "business_scope": context_company.get("business_scope", ""),
                "industry": context_company.get("industry", "")
            }
            logger.info(f"[CHAIN_REQUEST] transaction_analysis 链式调用输入: {json.dumps(transaction_inputs, ensure_ascii=False)}")
            transaction_result = await self.chain_manager.run_chain("transaction_analysis", transaction_inputs)
            if not transaction_result.success:
                yield json.dumps({"success": False, "error": transaction_result.error})
                return
            transaction_data = transaction_result.data
            if isinstance(transaction_data, str):
                try:
                    transaction_data = json.loads(transaction_data)
                except Exception:
                    transaction_data = {}

            logger.info("[DEBUG] Transaction data: %s", transaction_data)
            
            # Step 2: Match subjects
            # Prepare subjects for matching (simplified approach)
            subjects_compact = [subj.get('科目名称') or subj.get('name') for subj in subjects_raw]
            
            subject_inputs = {
                "transaction_type": transaction_data.get("transaction_type", ""),
                "content": transaction_data.get("content", ""),
                "context": transaction_data.get("context", ""),
                "amount": transaction_data.get("primary_amount", 0),
                "subjects": subjects_compact,
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "user_id": user_id,
                "company_name": context_company.get("name", ""),
                "business_scope": context_company.get("business_scope", ""),
                "industry": context_company.get("industry", ""),
                "accounting_standards": context_company.get("accounting_standards", "")
            }
            logger.info(f"[CHAIN_REQUEST] subject_matching 链式调用输入: {json.dumps(subject_inputs, ensure_ascii=False)}")
            subject_result = await self.chain_manager.run_chain("subject_matching", subject_inputs)
            if not subject_result.success:
                yield json.dumps({"success": False, "error": subject_result.error})
                return
            subject_data = subject_result.data
            if isinstance(subject_data, str):
                try:
                    subject_data = json.loads(subject_data)
                except Exception:
                    subject_data = {}
            
            # 转换 subject_data 格式，将 matched_entries 分为 debit_subjects 和 credit_subjects
            if "matched_entries" in subject_data:
                debit_subjects = []
                credit_subjects = []
                for entry in subject_data["matched_entries"]:
                    # 确保 entry 是字典类型
                    if isinstance(entry, dict):
                        if entry.get("direction") == "debit":
                            debit_subjects.append(entry)
                        elif entry.get("direction") == "credit":
                            credit_subjects.append(entry)
                # 更新 subject_data 以匹配后续处理的期望格式
                subject_data["debit_subjects"] = debit_subjects
                subject_data["credit_subjects"] = credit_subjects
            
            logger.info("[DEBUG] Subject data: %s", subject_data)

            # Process the entire chain with retry mechanism
            voucher_result = await self._process_chain_with_retry(
                enhanced_message, context_data, user_id
            )
            
            if not voucher_result["success"]:
                yield json.dumps({"success": False, "error": voucher_result["error"]})
                return
            
            data = voucher_result["data"]
            self.memory.add_ai_message(json.dumps(data, ensure_ascii=False))
            yield json.dumps(data, ensure_ascii=False)
            return

        except asyncio.CancelledError:
            logger.warning("Stream message was cancelled by client")
            yield json.dumps({"success": False, "error": "请求被用户取消"})
            return
        except Exception as e:
            logger.error(f"流式处理消息失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg
    
    async def process_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理文档"""
        logger.info(f"Entering process_document with document_type={document_type}, user_id={user_id}")
        if not self.llm_manager.is_configured():
            return {"success": False, "error": "LLM未配置"}
        
        try:
            # 执行文档分析链
            chain_inputs = {
                "document_type": document_type,
                "document_content": document_content,
                "user_id": user_id
            }
            
            logger.info(f"[CHAIN_REQUEST] document_analysis 链式调用输入: {json.dumps(chain_inputs, ensure_ascii=False)}")
            result = await self.chain_manager.run_chain("document_analysis", chain_inputs)
            
            if not result.success:
                return {"success": False, "error": result.error}
            
            # 保存到记忆
            self.memory.add_user_message(f"[文档] {document_type}")
            self.memory.add_ai_message(result.data)
            
            return {
                "success": True,
                "data": result.data,
                "session_id": self.session_id
            }
        except Exception as e:
            logger.error(f"处理文档失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def stream_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """流式处理文档"""
        logger.info(f"Entering stream_document with document_type={document_type}, user_id={user_id}")
        if not self.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        
        try:
            # 保存到记忆
            self.memory.add_user_message(f"[文档] {document_type}")
            
            # 准备系统消息和用户消息
            system_prompt = self.prompt_manager.build_prompt("system_base", user_id=user_id)
            document_prompt = self.prompt_manager.build_prompt(
                "document_analysis",
                document_type=document_type,
                document_content=document_content,
                user_id=user_id
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=document_prompt)
            ]
            
            # 流式生成
            full_response = ""
            # 记录发送给AI服务器的消息内容
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            logger.info(f"[STREAM_DOCUMENT_REQUEST] 发送给AI服务器的文档流式消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
            
            async for token in self.llm_manager.stream(messages):
                full_response += token
                yield token
            
            # 保存完整回复到记忆
            self.memory.add_ai_message(full_response)
            
        except Exception as e:
            logger.error(f"流式处理文档失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg
    
    async def execute_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """执行工具"""
        logger.info(f"Entering execute_tool with tool_name={tool_name}, kwargs={kwargs}")
        try:
            result = await self.tool_registry.execute_tool(tool_name, **kwargs)

            logger.info(f"execute_tool, tool_name: {tool_name}, result: {result}")

            return {
                "success": result.success,
                "data": result.data,
                "error": result.error,
                "execution_time": result.execution_time,
                "tool_name": result.tool_name
            }
        except Exception as e:
            logger.error(f"执行工具失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _process_voucher_with_retry(self, transaction_data: Dict[str, Any], subject_data: Dict[str, Any],
                                           amount_data: Dict[str, Any], context_data: Dict[str, Any],
                                           user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理凭证生成并自动重试以纠正不平衡"""
        logger.info("开始处理凭证生成并自动重试以纠正不平衡")
        
        # 初始化重试参数
        max_attempts = 3
        attempt_count = 0
        adjustment_suggestions = None
        previous_attempts = []
        
        context_company = context_data.get("company", {})
        
        while attempt_count < max_attempts:
            attempt_count += 1
            logger.info(f"凭证生成尝试 {attempt_count}/{max_attempts}")
            
            # Step 1: Generate voucher with adjustment information if available
            voucher_inputs = {
                "date": transaction_data.get("date", datetime.now().strftime("%Y-%m-%d")),
                "summary": transaction_data.get("summary", ""),
                "debit_subjects": subject_data.get("debit_subjects", []),
                "credit_subjects": subject_data.get("credit_subjects", []),
                "amount_breakdown": amount_data.get("amount_breakdown", {}),
                "tax_details": amount_data.get("tax_details", {}),
                "other_fees": amount_data.get("other_fees", {}),
                "retry_count": attempt_count - 1,  # 0 for first attempt
                "adjustment_suggestions": adjustment_suggestions,
                "previous_attempts": previous_attempts
            }
            
            logger.info(f"[CHAIN_REQUEST] voucher_generation_improved 链式调用输入: {json.dumps(voucher_inputs, ensure_ascii=False)}")
            voucher_result = await self.chain_manager.run_chain("voucher_generation_improved", voucher_inputs)
            if not voucher_result.success:
                return {"success": False, "error": voucher_result.error}
            voucher_data = voucher_result.data
            if isinstance(voucher_data, str):
                try:
                    voucher_data = json.loads(voucher_data)
                except Exception:
                    voucher_data = {}
            
            logger.info(f"Voucher data: {voucher_data}")

            # Step 2: Mathematically verify balance
            voucher_content = voucher_data.get("voucher", {})
            balance_check_result = await self.execute_tool(
                "balance_checker",
                debit_entries=voucher_content.get("debit_entries", []),
                credit_entries=voucher_content.get("credit_entries", [])
            )
            if not balance_check_result["success"]:
                return {"success": False, "error": balance_check_result["error"]}
            balance_check_data = balance_check_result["data"]

            logger.info(f"[DEBUG] Balance check data: {balance_check_data}")

            # Step 3: Verify balance with LLM
            balance_inputs = {
                "debit_entries": voucher_content.get("debit_entries", []),
                "credit_entries": voucher_content.get("credit_entries", [])
            }
            logger.info(f"[CHAIN_REQUEST] balance_verification 链式调用输入: {json.dumps(balance_inputs, ensure_ascii=False)}")
            balance_result = await self.chain_manager.run_chain("balance_verification", balance_inputs)
            if not balance_result.success:
                return {"success": False, "error": balance_result.error}
            balance_data = balance_result.data
            if isinstance(balance_data, str):
                try:
                    balance_data = json.loads(balance_data)
                except Exception:
                    balance_data = {}

            # Combine balance verification results
            combined_balance_data = {
                **balance_data,
                "mathematical_check": balance_check_data
            }

            logger.info(f"[DEBUG] Combined balance data: {combined_balance_data}")
            
            # Check if balanced
            is_balanced = (balance_data.get("is_balanced", False) and
                          balance_check_data.get("is_balanced", False))
            
            # Store attempt information
            attempt_info = {
                "attempt": attempt_count,
                "voucher_data": voucher_data,
                "balance_verification": combined_balance_data,
                "is_balanced": is_balanced
            }
            previous_attempts.append(attempt_info)
            
            # If balanced or max attempts reached, break
            if is_balanced or attempt_count >= max_attempts:
                break
            
            # Prepare adjustment suggestions for next attempt
            adjustment_suggestions = balance_data.get("adjustment_suggestions", [])
            logger.info(f"凭证不平衡，调整建议: {adjustment_suggestions}")
        
        # Combine all data into the final response
        final_voucher_data = previous_attempts[-1]["voucher_data"]
        final_balance_data = previous_attempts[-1]["balance_verification"]
        
        data = {
            "analysis": "凭证已生成",
            "action": "create_card",
            "is_finished": True,
            "cards": [{
                "type": "voucher",
                "op": "create",
                "data": final_voucher_data.get("voucher", {})
            }],
            "validation": final_balance_data,
            "retry_info": {
                "attempts": attempt_count,
                "max_attempts": max_attempts,
                "all_attempts": previous_attempts
            }
        }
        
        return {
            "success": True,
            "data": data
        }
    
    async def _process_chain_with_retry(self, enhanced_message: str, context_data: Dict[str, Any],
                                         user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理整个链式调用过程并自动重试以纠正不平衡"""
        logger.info("开始处理整个链式调用过程并自动重试以纠正不平衡")
        
        # 初始化重试参数
        max_attempts = 3
        attempt_count = 0
        adjustment_suggestions = None
        previous_attempts = []
        subjects_raw = context_data.get("subjects", [])
        context_company = context_data.get("company", {})
        
        while attempt_count < max_attempts:
            attempt_count += 1
            logger.info(f"链式调用尝试 {attempt_count}/{max_attempts}")
            
            # Step 1: Analyze the transaction with adjustment information if available
            transaction_inputs = {
                "user_input": enhanced_message,
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "company_name": context_company.get("name", ""),
                "business_scope": context_company.get("business_scope", ""),
                "industry": context_company.get("industry", ""),
                "current_attempt": attempt_count,
                "max_attempts": max_attempts,
                "adjustment_suggestions": adjustment_suggestions if adjustment_suggestions else ""
            }
            logger.info(f"[CHAIN_REQUEST] transaction_analysis 链式调用输入: {json.dumps(transaction_inputs, ensure_ascii=False)}")
            transaction_result = await self.chain_manager.run_chain("transaction_analysis", transaction_inputs)
            if not transaction_result.success:
                return {"success": False, "error": transaction_result.error}
            transaction_data = transaction_result.data
            if isinstance(transaction_data, str):
                try:
                    transaction_data = json.loads(transaction_data)
                except Exception:
                    transaction_data = {}
            
            logger.info("[DEBUG] Transaction data: %s", transaction_data)

            # Step 2: Match subjects with adjustment information if available
            subjects_compact = [subj.get('科目名称') or subj.get('name') for subj in subjects_raw]
            subject_inputs = {
                "transaction_type": transaction_data.get("transaction_type", ""),
                "content": transaction_data.get("content", ""),
                "context": transaction_data.get("context", ""),
                "amount": transaction_data.get("primary_amount", 0),
                "subjects": subjects_compact,
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "user_id": user_id,
                "company_name": context_company.get("name", ""),
                "business_scope": context_company.get("business_scope", ""),
                "industry": context_company.get("industry", ""),
                "accounting_standards": context_company.get("accounting_standards", ""),
                "current_attempt": attempt_count,
                "max_attempts": max_attempts,
                "adjustment_suggestions": adjustment_suggestions if adjustment_suggestions else ""
            }
            logger.info(f"[CHAIN_REQUEST] subject_matching 链式调用输入: {json.dumps(subject_inputs, ensure_ascii=False)}")
            subject_result = await self.chain_manager.run_chain("subject_matching", subject_inputs)
            if not subject_result.success:
                return {"success": False, "error": subject_result.error}
            subject_data = subject_result.data
            if isinstance(subject_data, str):
                try:
                    subject_data = json.loads(subject_data)
                except Exception:
                    subject_data = {}
            
            # 转换 subject_data 格式，将 matched_entries 分为 debit_subjects 和 credit_subjects
            if "matched_entries" in subject_data:
                debit_subjects = []
                credit_subjects = []
                for entry in subject_data["matched_entries"]:
                    # 确保 entry 是字典类型
                    if isinstance(entry, dict):
                        if entry.get("direction") == "debit":
                            debit_subjects.append(entry)
                        elif entry.get("direction") == "credit":
                            credit_subjects.append(entry)
                # 更新 subject_data 以匹配后续处理的期望格式
                subject_data["debit_subjects"] = debit_subjects
                subject_data["credit_subjects"] = credit_subjects
            
            logger.info("[DEBUG] Subject data: %s", subject_data)

            # Step 3: Calculate amounts with adjustment information if available
            amount_inputs = {
                "transaction_type": transaction_data.get("transaction_type", ""),
                "content": transaction_data.get("content", ""),
                "context": transaction_data.get("context", ""),
                "primary_amount": transaction_data.get("primary_amount", 0),
                "secondary_amounts": transaction_data.get("secondary_amounts", []),
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "user_id": user_id,
                "company_name": context_company.get("name", ""),
                "industry": context_company.get("industry", ""),
                "tax_info": context_company.get("tax_info", ""),
                "current_attempt": attempt_count,
                "max_attempts": max_attempts,
                "adjustment_suggestions": adjustment_suggestions if adjustment_suggestions else ""
            }
            logger.info(f"[CHAIN_REQUEST] amount_calculation 链式调用输入: {json.dumps(amount_inputs, ensure_ascii=False)}")
            amount_result = await self.chain_manager.run_chain("amount_calculation", amount_inputs)
            if not amount_result.success:
                return {"success": False, "error": amount_result.error}
            amount_data = amount_result.data
            if isinstance(amount_data, str):
                try:
                    amount_data = json.loads(amount_data)
                except Exception:
                    amount_data = {}

            logger.info("[DEBUG] Amount data: %s", amount_data)
            
            # Step 4: Generate voucher with adjustment information if available
            voucher_inputs = {
                "date": transaction_data.get("date", datetime.now().strftime("%Y-%m-%d")),
                "summary": transaction_data.get("summary", ""),
                "debit_subjects": subject_data.get("debit_subjects", []),
                "credit_subjects": subject_data.get("credit_subjects", []),
                "amount_breakdown": amount_data.get("amount_breakdown", {}),
                "tax_details": amount_data.get("tax_details", {}),
                "other_fees": amount_data.get("other_fees", {}),
                "retry_count": attempt_count - 1,  # 0 for first attempt
                "adjustment_suggestions": adjustment_suggestions if adjustment_suggestions else "",
                "previous_attempts": json.dumps(previous_attempts, ensure_ascii=False)
            }
            
            logger.info(f"[CHAIN_REQUEST] voucher_generation_improved 链式调用输入: {json.dumps(voucher_inputs, ensure_ascii=False)}")
            voucher_result = await self.chain_manager.run_chain("voucher_generation_improved", voucher_inputs)
            if not voucher_result.success:
                return {"success": False, "error": voucher_result.error}
            voucher_data = voucher_result.data
            if isinstance(voucher_data, str):
                try:
                    voucher_data = json.loads(voucher_data)
                except Exception:
                    voucher_data = {}
            
            logger.info(f"Voucher data: {voucher_data}")

            # Step 5: Mathematically verify balance
            voucher_content = voucher_data.get("voucher", {})
            balance_check_result = await self.execute_tool(
                "balance_checker",
                debit_entries=voucher_content.get("debit_entries", []),
                credit_entries=voucher_content.get("credit_entries", [])
            )
            if not balance_check_result["success"]:
                return {"success": False, "error": balance_check_result["error"]}
            balance_check_data = balance_check_result["data"]

            logger.info(f"[DEBUG] Balance check data: {balance_check_data}")

            # Step 6: Verify balance with LLM
            balance_inputs = {
                "debit_entries": voucher_content.get("debit_entries", []),
                "credit_entries": voucher_content.get("credit_entries", [])
            }
            logger.info(f"[CHAIN_REQUEST] balance_verification 链式调用输入: {json.dumps(balance_inputs, ensure_ascii=False)}")
            balance_result = await self.chain_manager.run_chain("balance_verification", balance_inputs)
            if not balance_result.success:
                return {"success": False, "error": balance_result.error}
            balance_data = balance_result.data
            if isinstance(balance_data, str):
                try:
                    balance_data = json.loads(balance_data)
                except Exception:
                    balance_data = {}

            # Combine balance verification results
            combined_balance_data = {
                **balance_data,
                "mathematical_check": balance_check_data
            }

            logger.info(f"[DEBUG] Combined balance data: {combined_balance_data}")
            
            # Check if balanced
            is_balanced = (balance_data.get("is_balanced", False) and
                          balance_check_data.get("is_balanced", False))
            
            # Store attempt information
            attempt_info = {
                "attempt": attempt_count,
                "transaction_data": transaction_data,
                "subject_data": subject_data,
                "amount_data": amount_data,
                "voucher_data": voucher_data,
                "balance_verification": combined_balance_data,
                "is_balanced": is_balanced
            }
            previous_attempts.append(attempt_info)
            
            # If balanced or max attempts reached, break
            if is_balanced or attempt_count >= max_attempts:
                break
            
            # Prepare adjustment suggestions for next attempt
            # Extract suggestion text from adjustment suggestions
            adjustment_list = balance_data.get("adjustment_suggestions", [])
            if adjustment_list and isinstance(adjustment_list, list):
                adjustment_suggestions = "\n".join([
                    item.get("suggestion", "") if isinstance(item, dict) else str(item)
                    for item in adjustment_list
                ])
            else:
                adjustment_suggestions = ""
            logger.info(f"凭证不平衡，调整建议: {adjustment_suggestions}")
        
        # Combine all data into the final response
        final_attempt = previous_attempts[-1]
        final_voucher_data = final_attempt["voucher_data"]
        final_balance_data = final_attempt["balance_verification"]
        
        data = {
            "analysis": "凭证已生成",
            "action": "create_card",
            "is_finished": True,
            "cards": [{
                "type": "voucher",
                "op": "create",
                "data": final_voucher_data.get("voucher", {})
            }],
            "validation": final_balance_data,
            "retry_info": {
                "attempts": attempt_count,
                "max_attempts": max_attempts,
                "all_attempts": previous_attempts
            }
        }
        
        return {
            "success": True,
            "data": data
        }
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        logger.info("Entering get_conversation_history")
        messages = self.memory.chat_history.get_messages()
        history = []
        
        for msg in messages:
            if isinstance(msg, HumanMessage):
                history.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                history.append({"role": "assistant", "content": msg.content})
            elif isinstance(msg, SystemMessage):
                history.append({"role": "system", "content": msg.content})
        
        return history
    
    def clear_history(self) -> None:
        """清空对话历史"""
        logger.info("Entering clear_history")
        self.memory.clear()
    
    async def _process_files(self, files: List[Dict]) -> str:
        """处理文件内容"""
        logger.info(f"Entering _process_files with {len(files) if files else 0} files")
        file_contents = []
        for file_info in files:
            try:
                file_path = file_info.get("file_path")
                logger.info(f"处理文件: {file_path}")  # 路径调试日志，改为 info
                file_name = file_info.get("file_name", "未知文件")
                file_type = file_info.get("file_type", "未知类型")
                
                if file_type == "image":
                    # OCR处理图片
                    result = await self.execute_tool("ocr_processor", file_path=file_path)
                    if result["success"]:
                        content = result["data"]["text"]
                        file_contents.append(f"文件：{file_name} (图片)\n内容：\n{content}\n")
                    else:
                        file_contents.append(f"文件：{file_name} (图片)\n处理失败：{result['error']}\n")
                elif file_type == "pdf":
                    # PDF处理
                    result = await self.execute_tool("pdf_processor", file_path=file_path)
                    if result["success"]:
                        content = result["data"]["text"]
                        file_contents.append(f"文件：{file_name} (PDF)\n内容：\n{content}\n")
                    else:
                        file_contents.append(f"文件：{file_name} (PDF)\n处理失败：{result['error']}\n")
                else:
                    file_contents.append(f"文件：{file_name}\n不支持的文件类型：{file_type}\n")
            except Exception as e:
                logger.error(f"处理文件失败: {str(e)}")
                file_contents.append(f"文件：{file_info.get('file_name', '未知文件')}\n处理失败：{str(e)}\n")
        
        return "\n".join(file_contents)

    async def _gather_context_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """异步收集上下文数据"""
        logger.info(f"Entering _gather_context_data with user_id={user_id}")
        db = AsyncSessionLocal(bind=engine)
        try:
            subjects_result = await db.execute(select(SubjectAccount))
            assets_result = await db.execute(select(Asset))
            staffs_result = await db.execute(select(Staff))
            # NEW: Collect company information
            company_result = await db.execute(select(Company))
            company = company_result.scalars().first()  # Assuming single company for now
            
            subjects = [
                {c.name: getattr(s, c.name) for c in s.__table__.columns}
                for s in subjects_result.scalars().all()
            ]
            assets = [
                {c.name: getattr(a, c.name) for c in a.__table__.columns}
                for a in assets_result.scalars().all()
            ]
            staff = [
                {c.name: getattr(st, c.name) for c in st.__table__.columns}
                for st in staffs_result.scalars().all()
            ]
        except asyncio.CancelledError:
            logger.warning("Database operation was cancelled, gracefully closing connection")
            try:
                await db.close()
            except Exception as e:
                logger.error(f"Error closing database connection during cancellation: {e}")
            raise
        except Exception as e:
            logger.error(f"Error gathering context data: {e}")
            try:
                await db.close()
            except Exception as close_error:
                logger.error(f"Error closing database connection: {close_error}")
            raise
        else:
            await db.close()
        from api.experience import get_experience
        experience = get_experience(user_id, limit=5) if user_id else []
        return {
            "subjects": subjects,
            "assets": assets,
            "staff": staff,
            "experience": experience,
            "company": {  # NEW: Add company data
                "name": company.name if company else "",
                "business_scope": company.business_scope if company else "",
                "industry": company.industry if company else "",
                "accounting_standards": company.accounting_standards if company else "",
                "tax_info": company.tax_id if company else ""
            } if company else None
        }

    # ============ 单据审核模式方法 ============
    
    async def stream_audit_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理单据审核消息"""
        logger.info(f"Entering stream_audit_message with message={message}, user_id={user_id}")
        if not self.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        
        try:
            # 处理文件内容
            file_content = ""
            if files and len(files) > 0:
                file_content = await self._process_files(files)
                # 将文件内容添加到用户消息中
                enhanced_message = f"{message}\n\n附件内容：\n{file_content}" if message.strip() else f"请分析以下文件内容并进行单据审核：\n{file_content}"
            else:
                enhanced_message = message

            logger.info(f"enhanced_message : {enhanced_message}")
            
            self.memory.add_user_message(enhanced_message)
            
            # 单据审核模式不需要收集完整的上下文数据，只需要公司信息
            context_data = {
                "company": None,  # 单据审核模式不需要公司信息
                "subjects": [],   # 单据审核模式不需要科目信息
                "assets": [],     # 单据审核模式不需要资产信息
                "staff": [],      # 单据审核模式不需要员工信息
                "experience": []  # 单据审核模式不需要经验信息
            }
            
            # 使用RAG搜索相关的规章制度
            rag_results = await self._search_audit_rag(enhanced_message)
            
            # 格式化RAG结果为字符串
            rag_results_str = ""
            if rag_results:
                rag_results_str = "\n".join([
                    f"- {result.get('title', '无标题')} ({result.get('category', '无分类')}): {result.get('content', '')}"
                    for result in rag_results
                ])
            
            # 准备系统提示和用户提示
            system_prompt = self.prompt_manager.build_prompt("audit_system_base", user_id=user_id)
            
            # 构建审核提示
            audit_prompt = self.prompt_manager.build_prompt(
                "audit_document",
                user_input=enhanced_message,
                rag_results=rag_results_str,
                current_date=datetime.now().strftime("%Y-%m-%d"),
                user_id=user_id
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=audit_prompt)
            ]
            
            # 流式生成
            full_response = ""
            # 记录发送给AI服务器的消息内容
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            logger.info(f"[STREAM_AUDIT_REQUEST] 发送给AI服务器的审核流式消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
            
            # 收集完整响应
            async for token in self.llm_manager.stream(messages):
                full_response += token
                # 流式返回原始token给前端显示
                yield token
            
            # 保存完整回复到记忆
            self.memory.add_ai_message(full_response)
            
            # 解析AI响应并生成结构化JSON
            try:
                # 尝试从响应中提取JSON结构
                audit_result = await self._parse_audit_response(full_response)
                
                # 生成最终的JSON响应
                final_response = {
                    "success": True,
                    "action": audit_result.get("action", "none"),
                    "answer": audit_result.get("answer", full_response),
                    "audit_conclusion": audit_result.get("audit_conclusion", "需要更多信息"),
                    "needs_more_info": audit_result.get("needs_more_info", False),
                    "required_info": audit_result.get("required_info", []),
                    "is_finished": audit_result.get("is_finished", True),
                    "analysis": audit_result.get("analysis", "")
                }
                
                # 返回结构化JSON响应
                yield json.dumps(final_response, ensure_ascii=False)
                
            except Exception as e:
                logger.error(f"解析审核响应失败: {str(e)}")
                # 如果解析失败，返回基本结构
                fallback_response = {
                    "success": True,
                    "action": "none",
                    "answer": full_response,
                    "audit_conclusion": "需要更多信息",
                    "needs_more_info": True,
                    "required_info": ["请提供更完整的单据信息"],
                    "is_finished": False,
                    "analysis": full_response
                }
                yield json.dumps(fallback_response, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"流式处理单据审核消息失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg
    
    async def _search_audit_rag(self, query: str) -> List[Dict]:
        """搜索单据审核相关的规章制度"""
        logger.info(f"Searching audit RAG with query: {query}")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 搜索相关文档
            result = rag_manager.search_documents(query, n_results=5)
            
            if result["success"]:
                # 格式化搜索结果
                rag_results = []
                for doc in result["data"]:
                    rag_results.append({
                        "title": doc["metadata"].get("title", ""),
                        "content": doc["content"],
                        "category": doc["metadata"].get("category", ""),
                        "source": doc["metadata"].get("source", "")
                    })
                return rag_results
            else:
                logger.error(f"RAG搜索失败: {result.get('error', '未知错误')}")
                return []
        except Exception as e:
            logger.error(f"搜索审核RAG失败: {str(e)}")
            return []
    
    async def add_audit_rag_data(self, data: List[Dict]) -> Dict:
        """添加单据审核相关的RAG数据"""
        logger.info(f"Adding audit RAG data: {len(data)} items")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 添加文档
            result = rag_manager.add_documents(data)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": result["message"],
                    "ids": result.get("ids", [])
                }
            else:
                logger.error(f"添加RAG数据失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }
        except Exception as e:
            logger.error(f"添加审核RAG数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def list_audit_rag_data(self) -> Dict:
        """列出单据审核相关的RAG数据"""
        logger.info("Listing audit RAG data")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 列出文档
            result = rag_manager.list_documents(limit=100, offset=0)
            
            if result["success"]:
                return {
                    "success": True,
                    "data": result["data"],
                    "count": result.get("count", 0)
                }
            else:
                logger.error(f"列出RAG数据失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }
        except Exception as e:
            logger.error(f"列出审核RAG数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def delete_audit_rag_data(self, item_id: str) -> Dict:
        """删除单据审核相关的RAG数据"""
        logger.info(f"Deleting audit RAG data: {item_id}")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 删除文档
            result = rag_manager.delete_document(item_id)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": result["message"]
                }
            else:
                logger.error(f"删除RAG数据失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }
        except Exception as e:
            logger.error(f"删除审核RAG数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def update_audit_rag_data(self, item_id: str, data: Dict) -> Dict:
        """更新单据审核相关的RAG数据"""
        logger.info(f"Updating audit RAG data: {item_id}")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 更新文档
            result = rag_manager.update_document(item_id, data)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": result["message"]
                }
            else:
                logger.error(f"更新RAG数据失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }
        except Exception as e:
            logger.error(f"更新审核RAG数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _parse_audit_response(self, response_text: str) -> Dict[str, Any]:
        """解析AI审核响应，提取结构化信息"""
        try:
            import re
            
            # 默认值
            result = {
                "action": "none",
                "answer": response_text,
                "audit_conclusion": "需要更多信息",
                "needs_more_info": False,
                "required_info": [],
                "is_finished": True,
                "analysis": response_text
            }
            
            # 尝试提取JSON格式的响应
            json_match = re.search(r'\{[\s\S]*\}', response_text)
            if json_match:
                try:
                    json_data = json.loads(json_match.group(0))
                    result.update(json_data)
                except json.JSONDecodeError:
                    pass
            
            # 分析文本内容，判断是否需要更多信息
            text_lower = response_text.lower()
            needs_info_keywords = [
                "请提供", "需要", "缺少", "不足", "补充", "请补充",
                "信息不足", "请上传", "请告知", "请说明"
            ]
            
            if any(keyword in text_lower for keyword in needs_info_keywords):
                result["needs_more_info"] = True
                result["audit_conclusion"] = "需要更多信息"
                result["action"] = "request_more_info"
                
                # 尝试提取需要的信息列表
                info_patterns = [
                    r'(?:请提供|需要|缺少|不足|补充|请补充)\s*[:：]?\s*([^\n\r。]+)',
                    r'(?:信息|内容|资料)\s*[:：]?\s*([^\n\r。]+)',
                    r'(?:请上传|请告知|请说明)\s*[:：]?\s*([^\n\r。]+)'
                ]
                
                required_info = []
                for pattern in info_patterns:
                    matches = re.findall(pattern, response_text)
                    if matches:
                        required_info.extend(matches)
                
                # 清理和过滤结果
                cleaned_info = []
                for info in required_info:
                    # 移除引号、花括号等特殊字符
                    cleaned = re.sub(r'["{}]', '', info.strip())
                    # 只保留有意义的文本
                    if len(cleaned) > 2 and not cleaned.startswith('\"'):
                        cleaned_info.append(cleaned)
                
                if cleaned_info:
                    result["required_info"] = list(set(cleaned_info))  # 去重
                else:
                    result["required_info"] = ["请提供更完整的单据信息"]
            
            # 检查审核结论
            if "审核通过" in response_text or "审核完成" in response_text:
                result["audit_conclusion"] = "审核通过"
                result["action"] = "audit_complete"
                result["needs_more_info"] = False
            elif "审核不通过" in response_text or "审核拒绝" in response_text:
                result["audit_conclusion"] = "审核不通过"
                result["action"] = "audit_rejected"
                result["needs_more_info"] = False
            
            return result
            
        except Exception as e:
            logger.error(f"解析审核响应失败: {str(e)}")
            return {
                "action": "none",
                "answer": response_text,
                "audit_conclusion": "需要更多信息",
                "needs_more_info": True,
                "required_info": ["请提供更完整的单据信息"],
                "is_finished": False,
                "analysis": response_text
            }


# 全局智能体执行器实例字典
_agent_executors: Dict[str, AgentExecutor] = {}

def get_agent_executor(session_id: Optional[str] = None) -> AgentExecutor:
    """获取智能体执行器实例"""
    logger.info(f"Entering get_agent_executor with session_id={session_id}")
    global _agent_executors
    
    if not session_id:
        session_id = str(uuid.uuid4())
    
    if session_id not in _agent_executors:
        _agent_executors[session_id] = AgentExecutor(session_id)
    
    return _agent_executors[session_id]


# 规章制度解析相关功能
class RegulationParser:
    """规章制度解析器"""
    
    def __init__(self, agent_executor: AgentExecutor):
        self.agent = agent_executor
        self.max_chunk_size = 8000  # 每个chunk的最大字符数
        self.chunk_overlap = 200   # chunk之间的重叠字符数
    
    def _split_document_into_chunks(self, document_content: str) -> List[str]:
        """将长文档分割成多个chunk"""
        logger.info(f"开始分割文档，原始文档长度: {len(document_content)} 字符")
        
        # 如果文档长度不超过最大chunk大小，直接返回
        if len(document_content) <= self.max_chunk_size:
            logger.info("文档长度较短，无需分割")
            return [document_content]
        
        chunks = []
        start_pos = 0
        
        while start_pos < len(document_content):
            # 计算当前chunk的结束位置
            end_pos = min(start_pos + self.max_chunk_size, len(document_content))
            
            # 如果不是最后一个chunk，尝试在句子边界处分割
            if end_pos < len(document_content):
                # 寻找最近的句子结束符（。！？.!?）
                for i in range(end_pos - 1, max(start_pos, end_pos - 500), -1):
                    if document_content[i] in '。！？.!?':
                        end_pos = i + 1
                        break
                else:
                    # 如果没有找到句子结束符，寻找段落分隔符
                    for i in range(end_pos - 1, max(start_pos, end_pos - 300), -1):
                        if document_content[i] == '\n' and document_content[i-1:i+2] != '\n\n':
                            end_pos = i + 1
                            break
            
            # 提取当前chunk
            chunk = document_content[start_pos:end_pos].strip()
            
            # 为chunk添加上下文信息（除了第一个chunk）
            if start_pos > 0:
                chunk = f"[文档片段，从位置 {start_pos} 开始]\n{chunk}"
            
            chunks.append(chunk)
            logger.debug(f"创建chunk {len(chunks)}: 位置 {start_pos}-{end_pos}, 长度 {len(chunk)} 字符")
            
            # 更新下一个chunk的起始位置，考虑重叠
            start_pos = end_pos - self.chunk_overlap if end_pos < len(document_content) else end_pos
        
        logger.info(f"文档分割完成，共生成 {len(chunks)} 个chunk")
        return chunks
    
    async def parse_audit_regulations(self, document_content: str) -> Dict[str, Any]:
        """解析规章制度文档，提取分类规则"""
        logger.info("开始解析规章制度文档")
        
        if not self.agent.llm_manager.is_configured():
            return {"success": False, "error": "LLM未配置"}
        
        try:
            # 检查文档长度，决定是否需要分块处理
            if len(document_content) > self.max_chunk_size:
                logger.info(f"文档较长({len(document_content)}字符)，将采用分块处理")
                return await self._parse_document_in_chunks(document_content)
            else:
                logger.info(f"文档较短({len(document_content)}字符)，将直接处理")
                return await self._parse_single_chunk(document_content)
                
        except Exception as e:
            logger.error(f"解析规章制度失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _parse_single_chunk(self, document_content: str) -> Dict[str, Any]:
        """解析单个文档chunk"""
        try:
            # 构建解析提示
            parse_prompt = self._build_regulation_parse_prompt(document_content)
            
            # 发送请求给LLM
            messages = [
                SystemMessage(content="你是一个专业的财务规章制度解析专家，擅长从文档中提取和分类各种报销规定。"),
                HumanMessage(content=parse_prompt)
            ]
            
            # 记录发送给AI服务器的消息内容
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            logger.info(f"[REGULATION_PARSE_REQUEST] 发送给AI服务器的规章制度解析消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
            
            # 获取LLM响应
            response = await self.agent.llm_manager.get_llm().ainvoke(messages)
            response_content = response.content
            
            # 尝试解析JSON响应
            try:
                parsed_data = json.loads(response_content)
                return {
                    "success": True,
                    "data": parsed_data,
                    "raw_response": response_content,
                    "chunk_count": 1
                }
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试提取JSON部分
                import re
                json_match = re.search(r'\{[\s\S]*\}', response_content)
                if json_match:
                    try:
                        parsed_data = json.loads(json_match.group())
                        return {
                            "success": True,
                            "data": parsed_data,
                            "raw_response": response_content,
                            "chunk_count": 1
                        }
                    except json.JSONDecodeError:
                        pass
                
                return {
                    "success": False,
                    "error": "无法解析AI响应为JSON格式",
                    "raw_response": response_content
                }
                
        except Exception as e:
            logger.error(f"解析单个文档chunk失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _parse_document_in_chunks(self, document_content: str) -> Dict[str, Any]:
        """分块解析长文档"""
        logger.info("开始分块解析文档")
        
        try:
            # 将文档分割成多个chunk
            chunks = self._split_document_into_chunks(document_content)
            chunk_results = []
            
            # 逐个解析chunk
            for i, chunk in enumerate(chunks):
                logger.info(f"正在解析第 {i+1}/{len(chunks)} 个chunk")
                
                # 为分块解析构建特殊的提示
                chunk_prompt = self._build_chunk_parse_prompt(chunk, i+1, len(chunks))
                
                # 发送请求给LLM
                messages = [
                    SystemMessage(content="你是一个专业的财务规章制度解析专家，擅长从文档片段中提取和分类各种报销规定。"),
                    HumanMessage(content=chunk_prompt)
                ]
                
                # 记录发送给AI服务器的消息内容
                formatted_messages = []
                for msg in messages:
                    if hasattr(msg, 'content'):
                        if msg.__class__.__name__ == 'SystemMessage':
                            formatted_messages.append({"role": "system", "content": msg.content})
                        elif msg.__class__.__name__ == 'HumanMessage':
                            formatted_messages.append({"role": "user", "content": msg.content})
                        elif msg.__class__.__name__ == 'AIMessage':
                            formatted_messages.append({"role": "assistant", "content": msg.content})
                
                logger.info(f"[REGULATION_CHUNK_PARSE_REQUEST] 发送给AI服务器的规章制度分块解析消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
                
                # 获取LLM响应
                response = await self.agent.llm_manager.get_llm().ainvoke(messages)
                response_content = response.content
                
                # 尝试解析JSON响应
                try:
                    chunk_data = json.loads(response_content)
                    chunk_results.append({
                        "chunk_index": i,
                        "chunk_data": chunk_data,
                        "raw_response": response_content
                    })
                    logger.info(f"第 {i+1} 个chunk解析成功")
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试提取JSON部分
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', response_content)
                    if json_match:
                        try:
                            chunk_data = json.loads(json_match.group())
                            chunk_results.append({
                                "chunk_index": i,
                                "chunk_data": chunk_data,
                                "raw_response": response_content
                            })
                            logger.info(f"第 {i+1} 个chunk解析成功（提取JSON）")
                        except json.JSONDecodeError:
                            logger.warning(f"第 {i+1} 个chunk解析失败，无法解析JSON")
                            chunk_results.append({
                                "chunk_index": i,
                                "chunk_data": None,
                                "raw_response": response_content,
                                "error": "无法解析JSON"
                            })
                    else:
                        logger.warning(f"第 {i+1} 个chunk解析失败，无有效JSON")
                        chunk_results.append({
                            "chunk_index": i,
                            "chunk_data": None,
                            "raw_response": response_content,
                            "error": "无有效JSON"
                        })
            
            # 合并所有chunk的解析结果
            merged_result = self._merge_chunk_results(chunk_results)
            
            return {
                "success": True,
                "data": merged_result,
                "chunk_results": chunk_results,
                "chunk_count": len(chunks)
            }
            
        except Exception as e:
            logger.error(f"分块解析文档失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _build_chunk_parse_prompt(self, chunk_content: str, chunk_index: int, total_chunks: int) -> str:
        """构建分块解析提示"""
        return f"""
请仔细分析以下规章制度文档片段（第 {chunk_index}/{total_chunks} 片段），提取其中的报销规定，并按照报销类型进行分类整理。

文档片段：
---
{chunk_content}
---

请按照以下JSON格式返回解析结果：
```json
{{
  "regulation_categories": [
    {{
      "type": "差旅费",
      "description": "与员工出差相关的费用报销规定",
      "rules": [
        "交通标准：飞机经济舱、高铁二等座、普通列车硬卧",
        "住宿标准：一线城市不超过600元/天，二线城市不超过500元/天，三线城市不超过400元/天",
        "伙食补贴：100元/天",
        "交通补贴：50元/天"
      ]
    }},
    {{
      "type": "办公用品",
      "description": "办公用品采购和报销相关规定",
      "rules": [
        "单价超过1000元的需部门经理审批",
        "超过5000元的需分管副总审批",
        "采购后需在3个工作日内完成报销",
        "需提供正规发票和采购清单"
      ]
    }}
  ]
}}
```

要求：
1. 仔细阅读文档片段内容，识别所有报销类型
2. 为每种类型创建一个分类对象
3. 提取该类型下的具体规定作为rules数组
4. 确保rules数组中的每条规则都是清晰、完整的句子
5. 如果片段中没有提到某种类型，请勿创建该类型
6. 确保返回的是有效的JSON格式
7. 这是文档的第 {chunk_index}/{total_chunks} 片段，请专注于当前片段的内容
"""
    
    def _merge_chunk_results(self, chunk_results: List[Dict]) -> Dict[str, Any]:
        """合并多个chunk的解析结果"""
        logger.info("开始合并多个chunk的解析结果")
        
        try:
            merged_categories = {}
            category_type_mapping = {}  # 用于处理类型名称的标准化
            
            # 处理每个chunk的结果
            for chunk_result in chunk_results:
                if chunk_result.get("chunk_data") and "regulation_categories" in chunk_result["chunk_data"]:
                    categories = chunk_result["chunk_data"]["regulation_categories"]
                    
                    for category in categories:
                        category_type = category.get("type", "").strip()
                        
                        if not category_type:
                            continue
                        
                        # 标准化类型名称（处理可能的变体）
                        normalized_type = self._normalize_category_type(category_type)
                        
                        # 如果该类型已存在，合并规则
                        if normalized_type in merged_categories:
                            existing_category = merged_categories[normalized_type]
                            
                            # 合并描述（取更详细的描述）
                            existing_desc = existing_category.get("description", "")
                            new_desc = category.get("description", "")
                            if len(new_desc) > len(existing_desc):
                                existing_category["description"] = new_desc
                            
                            # 合并规则（去重）
                            existing_rules = set(existing_category.get("rules", []))
                            new_rules = category.get("rules", [])
                            
                            for rule in new_rules:
                                # 规则去重处理
                                rule_cleaned = rule.strip()
                                if rule_cleaned and not self._is_duplicate_rule(rule_cleaned, existing_rules):
                                    existing_rules.add(rule_cleaned)
                            
                            existing_category["rules"] = list(existing_rules)
                            logger.debug(f"合并类型 '{normalized_type}'，现有规则数: {len(existing_category['rules'])}")
                        else:
                            # 新类型，直接添加
                            merged_categories[normalized_type] = {
                                "type": normalized_type,
                                "description": category.get("description", ""),
                                "rules": category.get("rules", [])
                            }
                            logger.debug(f"添加新类型 '{normalized_type}'，规则数: {len(merged_categories[normalized_type]['rules'])}")
            
            # 转换为最终格式
            final_categories = list(merged_categories.values())
            
            logger.info(f"合并完成，共生成 {len(final_categories)} 个分类")
            for category in final_categories:
                logger.debug(f"分类 '{category['type']}' 包含 {len(category['rules'])} 条规则")
            
            return {
                "regulation_categories": final_categories,
                "merge_summary": {
                    "total_chunks": len(chunk_results),
                    "successful_chunks": len([r for r in chunk_results if r.get("chunk_data")]),
                    "merged_categories": len(final_categories)
                }
            }
            
        except Exception as e:
            logger.error(f"合并chunk结果失败: {str(e)}")
            return {"regulation_categories": []}
    
    def _normalize_category_type(self, category_type: str) -> str:
        """标准化分类类型名称"""
        # 定义类型名称的标准化映射
        type_mapping = {
            "差旅": "差旅费",
            "差旅费": "差旅费",
            "出差": "差旅费",
            "出差费用": "差旅费",
            "交通": "交通费",
            "交通费": "交通费",
            "住宿": "住宿费",
            "住宿费": "住宿费",
            "办公": "办公用品",
            "办公用品": "办公用品",
            "办公费": "办公用品",
            "招待": "业务招待费",
            "业务招待": "业务招待费",
            "业务招待费": "业务招待费",
            "招待费": "业务招待费",
            "通讯": "通讯费",
            "通讯费": "通讯费",
            "电话": "通讯费",
            "电话费": "通讯费",
            "培训": "培训费",
            "培训费": "培训费",
            "福利": "福利费",
            "福利费": "福利费"
        }
        
        # 精确匹配
        if category_type in type_mapping:
            return type_mapping[category_type]
        
        # 模糊匹配
        for key, value in type_mapping.items():
            if key in category_type or category_type in key:
                return value
        
        # 如果没有匹配，返回原类型
        return category_type
    
    def _is_duplicate_rule(self, new_rule: str, existing_rules: set) -> bool:
        """检查是否为重复规则"""
        new_rule_normalized = new_rule.lower().strip()
        
        for existing_rule in existing_rules:
            existing_rule_normalized = existing_rule.lower().strip()
            
            # 完全匹配
            if new_rule_normalized == existing_rule_normalized:
                return True
            
            # 高度相似（编辑距离或包含关系）
            if (len(new_rule_normalized) > 10 and len(existing_rule_normalized) > 10 and
                (new_rule_normalized in existing_rule_normalized or
                 existing_rule_normalized in new_rule_normalized)):
                return True
        
        return False
    
    def _build_regulation_parse_prompt(self, document_content: str) -> str:
        """构建规章制度解析提示"""
        return f"""
请仔细分析以下规章制度文档内容，提取其中的报销规定，并按照报销类型进行分类整理。

文档内容：
---
{document_content}
---

请按照以下JSON格式返回解析结果：
```json
{{
  "regulation_categories": [
    {{
      "type": "差旅费",
      "description": "与员工出差相关的费用报销规定",
      "rules": [
        "交通标准：飞机经济舱、高铁二等座、普通列车硬卧",
        "住宿标准：一线城市不超过600元/天，二线城市不超过500元/天，三线城市不超过400元/天",
        "伙食补贴：100元/天",
        "交通补贴：50元/天"
      ]
    }},
    {{
      "type": "办公用品",
      "description": "办公用品采购和报销相关规定",
      "rules": [
        "单价超过1000元的需部门经理审批",
        "超过5000元的需分管副总审批",
        "采购后需在3个工作日内完成报销",
        "需提供正规发票和采购清单"
      ]
    }},
    {{
      "type": "业务招待费",
      "description": "业务招待相关费用报销规定",
      "rules": [
        "需提前申请，注明招待对象、人数、预算标准",
        "一般客户招待标准不超过200元/人",
        "重要客户不超过500元/人",
        "需提供招待对象名单、消费清单和发票"
      ]
    }}
  ]
}}
```

要求：
1. 仔细阅读文档内容，识别所有报销类型
2. 为每种类型创建一个分类对象
3. 提取该类型下的具体规定作为rules数组
4. 确保rules数组中的每条规则都是清晰、完整的句子
5. 如果文档中没有提到某种类型，请勿创建该类型
6. 确保返回的是有效的JSON格式
"""
    
    async def save_parsed_regulations(self, parsed_data: Dict[str, Any], original_filename: str = "") -> Dict[str, Any]:
        """保存解析后的规章制度数据"""
        logger.info("保存解析后的规章制度数据")
        
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 检查是否有分块信息，如果有则使用专门的分块保存方法
            if parsed_data.get("merge_summary") and parsed_data.get("regulation_categories"):
                logger.info("检测到分块解析结果，使用add_chunked_regulation_document方法保存")
                result = rag_manager.add_chunked_regulation_document(parsed_data, original_filename)
            else:
                logger.info("未检测到分块解析结果，使用常规add_documents方法保存")
                # 将解析后的数据转换为RAG格式
                rag_documents = []
                
                for category in parsed_data.get("regulation_categories", []):
                    # 为每个分类创建一个文档
                    doc_content = f"类型：{category['type']}\n描述：{category['description']}\n\n规定：\n" + "\n".join([f"- {rule}" for rule in category.get("rules", [])])
                    
                    rag_documents.append({
                        "title": f"{category['type']}报销规定",
                        "content": doc_content,
                        "category": category['type'],
                        "source": original_filename or "规章制度文档解析"
                    })
                
                # 保存到RAG系统
                result = rag_manager.add_documents(rag_documents)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": result.get("message", f"成功保存解析后的规章制度数据"),
                    "saved_count": result.get("ids", []).__len__(),
                    "chunk_info": result.get("chunk_info", None)
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "保存失败")
                }
                
        except Exception as e:
            logger.error(f"保存解析后的规章制度失败: {str(e)}")
            return {"success": False, "error": str(e)}


# 扩展AgentExecutor类，添加规章制度解析功能
async def parse_audit_regulations(self, document_content: str) -> Dict[str, Any]:
    """解析规章制度文档"""
    parser = RegulationParser(self)
    return await parser.parse_audit_regulations(document_content)


async def save_parsed_regulations(self, parsed_data: Dict[str, Any], original_filename: str = "") -> Dict[str, Any]:
    """保存解析后的规章制度数据"""
    parser = RegulationParser(self)
    return await parser.save_parsed_regulations(parsed_data, original_filename)


# 将新方法添加到AgentExecutor类中
AgentExecutor.parse_audit_regulations = parse_audit_regulations
AgentExecutor.save_parsed_regulations = save_parsed_regulations
