import asyncio
from core.db import engine, Base, AsyncSessionLocal
from models.subject import SubjectAccount
from models.asset import Asset
from models.role_staff import Staff
from models.company import Company
from sqlalchemy.future import select

async def init_models():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("数据库表已初始化")

async def create_sample_companies():
    """Create sample companies for testing"""
    db = AsyncSessionLocal(bind=engine)
    try:
        # Check if sample companies already exist
        stmt = select(Company).where(Company.name.in_([
            "科技创新有限公司",
            "美味餐饮有限公司",
            "建筑工程有限公司",
            "广告传媒有限公司",
            "进出口贸易有限公司"
        ]))
        result = await db.execute(stmt)
        existing_companies = result.scalars().all()
        
        if len(existing_companies) >= 5:
            print("样本公司数据已存在")
            return
        
        # Create sample companies
        sample_companies = [
            Company(
                name="科技创新有限公司",
                business_scope="软件开发、技术咨询服务、计算机硬件销售",
                industry="信息技术服务业",
                company_size="中小型企业",
                tax_id="911100001234567890",
                accounting_standards="企业会计准则"
            ),
            Company(
                name="美味餐饮有限公司",
                business_scope="餐饮服务、食品销售",
                industry="住宿和餐饮业",
                company_size="小型企业",
                tax_id="911100000987654321",
                accounting_standards="小企业会计准则"
            ),
            Company(
                name="建筑工程有限公司",
                business_scope="建筑工程施工、建筑材料销售、工程设计",
                industry="建筑业",
                company_size="大型企业",
                tax_id="911100001122334455",
                accounting_standards="企业会计准则"
            ),
            Company(
                name="广告传媒有限公司",
                business_scope="广告设计制作、媒体推广、市场营销策划",
                industry="文化、体育和娱乐业",
                company_size="中小型企业",
                tax_id="911100005566778899",
                accounting_standards="企业会计准则"
            ),
            Company(
                name="进出口贸易有限公司",
                business_scope="商品进出口贸易",
                industry="批发和零售业",
                company_size="中型企业",
                tax_id="911100009988776655",
                accounting_standards="企业会计准则"
            )
        ]
        
        # Add companies to session
        for company in sample_companies:
            # Check if company already exists
            stmt = select(Company).where(Company.name == company.name)
            result = await db.execute(stmt)
            existing = result.scalar_one_or_none()
            
            if not existing:
                db.add(company)
        
        await db.commit()
        print("样本公司数据已创建")
    finally:
        await db.close()

async def main():
    await init_models()
    await create_sample_companies()

if __name__ == "__main__":
    asyncio.run(main())